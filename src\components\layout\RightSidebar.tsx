import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Bookmark, Eye, Calendar, TrendingUp, TrendingDown, DollarSign, Percent, AlertTriangle } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const RightSidebar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewingScenario, setViewingScenario] = useState<string | null>(null);
  const { scenarios, selectedScenarios } = useDashboard();

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  // Mock data for demonstration if no scenarios exist
  const mockScenarios = [
    {
      id: '1',
      name: 'Face Amount Increase 25%',
      category: 'face-amount' as const,
      keyPoints: [
        'Death benefit increases to $625,000',
        'Annual premium increases by $6,250',
        'Better coverage ratio',
        'Long-term wealth protection'
      ],
      impact: 'positive',
      createdAt: new Date('2024-01-15'),
      data: {
        originalAmount: 500000,
        newAmount: 625000,
        premiumImpact: 6250
      }
    },
    {
      id: '2',
      name: 'Premium Reduction 10%',
      category: 'premium' as const,
      keyPoints: [
        'Annual premium reduces to $22,500',
        'Cash value growth slows',
        'Maintains policy viability',
        'Improved cash flow'
      ],
      impact: 'neutral',
      createdAt: new Date('2024-01-14'),
      data: {
        originalPremium: 25000,
        newPremium: 22500,
        cashValueImpact: -2500
      }
    },
    {
      id: '3',
      name: 'Loan Repayment Strategy',
      category: 'loan-repayment' as const,
      keyPoints: [
        'Eliminates $50,000 loan balance',
        'Saves $15,000 in interest',
        'Restores full death benefit',
        'Improves policy performance'
      ],
      impact: 'positive',
      createdAt: new Date('2024-01-13'),
      data: {
        loanAmount: 50000,
        interestSavings: 15000
      }
    }
  ];

  const displayScenarios = scenarios.length > 0 ? scenarios : mockScenarios;
  const filteredScenarios = displayScenarios.filter(scenario => 
    selectedScenarios.includes(scenario.id)
  );

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'face-amount': return DollarSign;
      case 'premium': return TrendingUp;
      case 'income': return TrendingDown;
      case 'loan-repayment': return Percent;
      case 'interest-rate': return Percent;
      case 'policy-lapse': return AlertTriangle;
      default: return Bookmark;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'face-amount': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'premium': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'income': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      case 'loan-repayment': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'interest-rate': return 'text-indigo-600 bg-indigo-100 dark:bg-indigo-900/20';
      case 'policy-lapse': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'positive': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'negative': return <TrendingDown className="w-4 h-4 text-red-600" />;
      default: return <TrendingUp className="w-4 h-4 text-yellow-600" />;
    }
  };

  const viewingScenarioData = viewingScenario ? 
    filteredScenarios.find(s => s.id === viewingScenario) : null;

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* Toggle Button */}
      <button
        onClick={toggleSidebar}
        className={`fixed top-1/2 transform -translate-y-1/2 z-50 w-8 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-l-md shadow-lg transition-all duration-300 flex items-center justify-center group ${
          isOpen ? 'right-80' : 'right-0'
        }`}
        aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
      >
        {isOpen ? (
          <ChevronRight className="w-4 h-4 transition-transform group-hover:scale-110" />
        ) : (
          <ChevronLeft className="w-4 h-4 transition-transform group-hover:scale-110" />
        )}
      </button>

      {/* Right Sidebar */}
      <div
        className={`fixed top-0 right-0 h-full w-80 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 shadow-2xl transform transition-transform duration-300 ease-in-out z-40 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center shadow-md">
              <Bookmark className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Selected Scenarios</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {filteredScenarios.length} scenario{filteredScenarios.length !== 1 ? 's' : ''} selected
              </p>
            </div>
          </div>
          {viewingScenario && (
            <button
              onClick={() => setViewingScenario(null)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto h-[calc(100vh-80px)]">
          {!viewingScenario ? (
            // Scenarios List
            <div className="p-4 space-y-4">
              {filteredScenarios.length === 0 ? (
                <div className="text-center py-8">
                  <Bookmark className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">No scenarios selected</p>
                  <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                    Select scenarios from the illustration tabs to view them here
                  </p>
                </div>
              ) : (
                filteredScenarios.map((scenario) => {
                  const CategoryIcon = getCategoryIcon(scenario.category);
                  const categoryColor = getCategoryColor(scenario.category);
                  // Type guard for keyPoints and impact
                  const hasKeyPoints = Array.isArray((scenario as any).keyPoints);
                  const impact = (scenario as any).impact || 'neutral';
                  return (
                    <div
                      key={scenario.id}
                      className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200"
                    >
                      {/* Scenario Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <div className={`p-1.5 rounded ${categoryColor}`}>
                            <CategoryIcon className="w-4 h-4" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                              {scenario.name}
                            </h4>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                                {scenario.category.replace('-', ' ')}
                              </span>
                              {getImpactIcon(impact)}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Key Points Preview */}
                      <div className="space-y-1 mb-3">
                        {hasKeyPoints && (scenario as any).keyPoints.slice(0, 2).map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-1.5 flex-shrink-0" />
                            <p className="text-xs text-gray-600 dark:text-gray-300">{point}</p>
                          </div>
                        ))}
                        {hasKeyPoints && (scenario as any).keyPoints.length > 2 && (
                          <p className="text-xs text-blue-600 dark:text-blue-400 ml-3.5">
                            +{(scenario as any).keyPoints.length - 2} more points
                          </p>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                          <Calendar className="w-3 h-3" />
                          <span>{scenario.createdAt?.toLocaleDateString()}</span>
                        </div>
                        <button
                          onClick={() => setViewingScenario(scenario.id)}
                          className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                        >
                          <Eye className="w-3 h-3" />
                          <span>View Details</span>
                        </button>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          ) : (
            // Detailed Scenario View
            viewingScenarioData && (
              <div className="p-4">
                <div className="space-y-6">
                  {/* Scenario Title */}
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
                      {viewingScenarioData.name}
                    </h3>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getCategoryColor(viewingScenarioData.category)}`}>
                        {viewingScenarioData.category.replace('-', ' ').toUpperCase()}
                      </span>
                      {getImpactIcon(((viewingScenarioData as any).impact) || 'neutral')}
                    </div>
                  </div>

                  {/* Key Points */}
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">Key Points</h4>
                    <div className="space-y-2">
                      {Array.isArray((viewingScenarioData as any).keyPoints) &&
                        (viewingScenarioData as any).keyPoints.map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-3">
                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                            <p className="text-sm text-gray-600 dark:text-gray-300">{point}</p>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* Financial Impact */}
                  {viewingScenarioData.data && (
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Financial Impact</h4>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
                        {Object.entries(viewingScenarioData.data).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {typeof value === 'number' ? 
                                (key.toLowerCase().includes('rate') || key.toLowerCase().includes('percent') ? 
                                  `${value}%` : `$${value.toLocaleString()}`) : 
                                String(value)
                              }
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Metadata */}
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Created</span>
                        <span className="text-gray-900 dark:text-white">
                          {viewingScenarioData.createdAt?.toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Category</span>
                        <span className="text-gray-900 dark:text-white capitalize">
                          {viewingScenarioData.category.replace('-', ' ')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}
        </div>
      </div>
    </>
  );
};

export default RightSidebar;