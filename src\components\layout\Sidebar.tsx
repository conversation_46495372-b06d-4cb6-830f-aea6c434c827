import React, { useState } from 'react';
import {
  BarChart3,
  FileText,
  Settings,
  TrendingUp,
  Shield,
  LogOut,
  ChevronDown,
  ChevronRight,
  Bookmark
} from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import { useAuth } from '../../contexts/AuthContext';

const Sidebar: React.FC = () => {
  const { activeTab, setActiveTab } = useDashboard();
  const { logout, user } = useAuth();
  const [illustrationExpanded, setIllustrationExpanded] = useState(false);


  const menuItems = [
    { id: 'dashboard', label: 'Dashboard Overview', icon: BarChart3 },
    { id: 'policy-selection', label: 'Policy Selection', icon: Shield },
  ];

  const illustrationCategories = [
    { id: 'face-amount', label: 'Face Amount' },
    { id: 'premium', label: 'Premium' },
    { id: 'income', label: 'Income (Loan & Withdrawal)' },
    { id: 'loan-repayment', label: 'Loan Repayment' },
    { id: 'interest-rate', label: 'Interest Rate Based' },
    { id: 'policy-lapse', label: 'Policy Lapse' },
  ];

  const bottomMenuItems = [
    { id: 'selected-scenarios', label: 'Selected illustrations', icon: Bookmark },
    { id: 'analysis-reports', label: 'Illustration Reports', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  const handleIllustrationClick = () => {
    if (illustrationExpanded) {
      // If already expanded, close it
      setIllustrationExpanded(false);
    } else {
      // If closed, open it and navigate to AS-IS
      setIllustrationExpanded(true);
      setActiveTab('as-is');
    }
  };



  const handleCategoryClick = (categoryId: string) => {
    setActiveTab(categoryId);
  };

  return (
    <div className="w-64 bg-gray-900 dark:bg-[#121212] text-white h-screen flex flex-col transition-all duration-300 shadow-lg dark:shadow-2xl">
      <div className="p-6 border-b border-gray-800">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 dark:bg-blue-500 rounded-lg flex items-center justify-center">
            <Shield className="w-6 h-6" />
          </div>
          <div>
            <h2 className="text-xl font-bold">Life Insurance</h2>
            <p className="text-xl text-white">Illustration Portal</p>
          </div>
        </div>
      </div>

      <div className="flex-1 py-6 overflow-y-auto scrollbar-thin scrollbar-track-gray-800 scrollbar-thumb-gray-600">
        <nav className="space-y-2 px-3">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-blue-600 dark:bg-blue-500 text-white'
                    : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            );
          })}

          {/* Illustration Manager with Dropdown */}
          <div>
            <button
              onClick={handleIllustrationClick}
              className={`w-full flex items-center justify-between px-3 py-3 rounded-lg text-left transition-all duration-200 ${
                activeTab === 'as-is' || illustrationExpanded
                  ? 'bg-blue-600 dark:bg-blue-500 text-white'
                  : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
              }`}
            >
              <div className="flex items-center space-x-3">
                <TrendingUp className="w-5 h-5" />
                <span className="font-medium">Illustration Options</span>
              </div>
              {illustrationExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>

            {illustrationExpanded && (
              <div className="ml-6 mt-2 space-y-1 border-l border-gray-700 pl-4">
                <button
                  onClick={() => setActiveTab('as-is')}
                  className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                    activeTab === 'as-is'
                      ? 'bg-blue-500 text-white'
                      : 'text-white/70 hover:text-white hover:bg-gray-800 dark:hover:bg-gray-700'
                  }`}
                >
                  AS-IS
                </button>

                {/* Direct category buttons without What-If wrapper */}
                {illustrationCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategoryClick(category.id)}
                    className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                      activeTab === category.id
                        ? 'bg-blue-500 text-white'
                        : 'text-white/70 hover:text-white hover:bg-gray-800 dark:hover:bg-gray-700'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {bottomMenuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left transition-all duration-200 ${
                  activeTab === item.id
                    ? 'bg-blue-600 dark:bg-blue-500 text-white'
                    : 'text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      <div className="p-6 border-t border-gray-800 dark:border-gray-700">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-gray-700 dark:bg-gray-600 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-sm font-medium">
              {user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
            </span>
          </div>
          <div>
            <p className="text-sm font-medium">{user?.name}</p>
            <p className="text-xs text-white/50">{user?.email}</p>
          </div>
        </div>
        <button
          onClick={logout}
          className="w-full flex items-center space-x-3 px-3 py-2 text-white/70 hover:bg-gray-800 dark:hover:bg-gray-700 hover:text-white rounded-lg transition-all duration-200"
        >
          <LogOut className="w-4 h-4" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;