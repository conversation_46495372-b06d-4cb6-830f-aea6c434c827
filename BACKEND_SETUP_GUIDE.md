# 🏥 Life Insurance Backend Setup Guide

This guide will help you set up and test the Python backend for persistent data storage.

## 🚀 Quick Start

### Step 1: Start the Python Backend Server

1. **Open a new terminal/command prompt**
2. **Navigate to the server directory:**
   ```bash
   cd server
   ```

3. **Start the server:**
   ```bash
   python main.py
   ```

4. **You should see output like this:**
   ```
   ============================================================
   🏥 Life Insurance Application Backend Server
   ============================================================
   📊 Using Python built-in modules only
   💾 Data storage: JSON files in ./data/
   🔐 Authentication: Basic Auth (demo/demo123)
   🌐 Server starting on http://localhost:8000
   ============================================================
   ✅ Server running on http://localhost:8000
   📋 Available endpoints:
      GET    /health                    - Health check
      GET    /api/scenarios             - Get all scenarios
      POST   /api/scenarios             - Create new scenario
      PUT    /api/scenarios/{id}        - Update scenario
      DELETE /api/scenarios/{id}        - Delete scenario
      GET    /api/scenarios/selected    - Get selected scenarios
      POST   /api/scenarios/selected    - Update selected scenarios

   🔑 Authentication: Basic Auth with username 'demo' and password 'demo123'

   ⏹️  Press Ctrl+C to stop the server
   ============================================================
   ```

### Step 2: Test the Backend (Optional)

1. **In another terminal, run the test script:**
   ```bash
   cd server
   python test_api.py
   ```

2. **You should see all tests pass:**
   ```
   🧪 Life Insurance API Test Suite
   ==================================================
   🔍 Testing health check...
   ✅ Health check passed: {'status': 'healthy', 'timestamp': '...'}
   
   🔍 Testing scenarios CRUD operations...
   📋 Getting scenarios...
   ✅ Got scenarios: 0 scenarios found
   ➕ Creating new scenario...
   ✅ Scenario created: {'message': 'Scenario created successfully'}
   ...
   
   🎉 All tests passed! The API is working correctly.
   ```

### Step 3: Start the Frontend

1. **In another terminal, start your React app:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

2. **Open your browser to the React app URL (usually http://localhost:5173)**

## 🧪 Testing Data Persistence

### Test Scenario Creation and Persistence:

1. **Create scenarios in different tabs:**
   - Go to **Illustration Manager** → **As-Is** tab
   - Fill out the form and click "Save AS-IS Configuration"
   - Go to **Face Amount** tab and create scenarios
   - Go to **Income** tab and create scenarios

2. **Select scenarios:**
   - Go to **Selected Scenarios** tab
   - Check the boxes next to scenarios you want to select
   - Notice the counter updates

3. **Test persistence:**
   - **Refresh the page** → Your scenarios and selections should remain
   - **Close the browser completely** → Reopen and your data should still be there
   - **Open in a different browser** → Same data should be available (same user account)

### Expected Behavior:

✅ **Before (localStorage):** Data lost on page refresh  
✅ **After (Python backend):** Data persists across:
- Page refreshes
- Browser restarts
- Different browser sessions
- Computer restarts (as long as server is running)

## 📁 Data Storage Location

Your data is stored in JSON files in the `server/data/` directory:

```
server/data/
├── scenarios.json          # All scenarios data
├── selected_scenarios.json # Selected scenarios per user
└── users.json             # User authentication data
```

You can view these files to see your data being saved in real-time.

## 🔧 Configuration

### Change Server Port:
Edit `server/main.py` and modify:
```python
HOST = 'localhost'
PORT = 8000  # Change this to your desired port
```

### Change Frontend API URL:
Edit `src/services/api.ts` and modify:
```typescript
const API_BASE_URL = 'http://localhost:8000';  // Change this to match your server
```

## 🐛 Troubleshooting

### Problem: "Connection refused" or "Failed to fetch"
**Solution:** Make sure the Python server is running on localhost:8000

### Problem: "Authentication required" errors
**Solution:** The demo credentials are hardcoded. Check that `src/services/api.ts` has:
```typescript
const DEMO_CREDENTIALS = {
  username: 'demo',
  password: 'demo123',
};
```

### Problem: Data not persisting
**Solution:** 
1. Check that the `server/data/` directory exists
2. Check file permissions
3. Look at server console for error messages

### Problem: CORS errors
**Solution:** The server includes CORS headers. If you still get CORS errors:
1. Make sure you're accessing the frontend through the dev server (not file://)
2. Check that the API_BASE_URL in `api.ts` matches your server URL exactly

## 🔄 Migration from localStorage

The system automatically migrates from localStorage to backend storage:

1. **First time:** Data loads from backend (empty initially)
2. **Existing localStorage data:** Will be lost, but you can manually recreate scenarios
3. **Going forward:** All data is stored on the backend

## 🚀 Production Deployment

For production use, consider:

1. **Database:** Replace JSON files with SQLite/PostgreSQL
2. **Authentication:** Implement proper JWT/OAuth instead of Basic Auth
3. **HTTPS:** Use SSL certificates
4. **Process Manager:** Use PM2, systemd, or Docker
5. **Reverse Proxy:** Use nginx or Apache
6. **Environment Variables:** For configuration
7. **Logging:** Implement proper logging system

## 📊 Monitoring

### Check Server Health:
```bash
curl http://localhost:8000/health
```

### View Server Logs:
The server prints logs to the console. Look for:
- Request logs with timestamps
- Error messages
- Data operation confirmations

## 🎯 Next Steps

1. ✅ **Backend is running** → Server started successfully
2. ✅ **Frontend connected** → No connection errors in browser console
3. ✅ **Data persisting** → Scenarios survive page refresh
4. ✅ **Multi-tab sync** → Changes appear across browser tabs
5. 🚀 **Ready for production** → Consider production deployment options

---

**🎉 Congratulations!** Your Life Insurance application now has persistent backend storage that survives browser sessions and page refreshes!
