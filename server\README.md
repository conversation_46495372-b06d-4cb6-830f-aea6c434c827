# Life Insurance Backend Server

A lightweight Python backend server using only built-in modules for data persistence.

## Features

- ✅ **Zero Dependencies**: Uses only Python built-in modules
- ✅ **JSON File Storage**: Simple file-based data persistence
- ✅ **REST API**: Full CRUD operations for scenarios
- ✅ **Authentication**: Basic Auth with demo user
- ✅ **CORS Support**: Ready for frontend integration
- ✅ **User Isolation**: Each user's data is separate

## Quick Start

1. **Start the server:**
   ```bash
   cd server
   python main.py
   ```

2. **Server will start on:** `http://localhost:8000`

3. **Demo credentials:**
   - Username: `demo`
   - Password: `demo123`

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Scenarios Management
- `GET /api/scenarios` - Get all scenarios for authenticated user
- `POST /api/scenarios` - Create new scenario
- `PUT /api/scenarios/{id}` - Update existing scenario
- `DELETE /api/scenarios/{id}` - Delete scenario

### Selected Scenarios
- `GET /api/scenarios/selected` - Get selected scenario IDs
- `POST /api/scenarios/selected` - Update selected scenario IDs

## Authentication

All API endpoints (except `/health`) require Basic Authentication:

```
Authorization: Basic base64(username:password)
```

Example with curl:
```bash
curl -H "Authorization: Basic ZGVtbzpkZW1vMTIz" http://localhost:8000/api/scenarios
```

## Data Storage

Data is stored in JSON files in the `./data/` directory:
- `scenarios.json` - All scenarios data
- `selected_scenarios.json` - Selected scenarios per user
- `users.json` - User authentication data

## Example Usage

### Create a Scenario
```bash
curl -X POST http://localhost:8000/api/scenarios \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic ZGVtbzpkZW1vMTIz" \
  -d '{
    "name": "Test Scenario",
    "policyId": "POL-001",
    "asIsDetails": "Current policy details",
    "whatIfOptions": ["Option 1", "Option 2"],
    "category": "premium",
    "data": {}
  }'
```

### Get All Scenarios
```bash
curl -H "Authorization: Basic ZGVtbzpkZW1vMTIz" http://localhost:8000/api/scenarios
```

### Update Selected Scenarios
```bash
curl -X POST http://localhost:8000/api/scenarios/selected \
  -H "Content-Type: application/json" \
  -H "Authorization: Basic ZGVtbzpkZW1vMTIz" \
  -d '{"selectedScenarios": ["scenario-id-1", "scenario-id-2"]}'
```

## Configuration

Edit the `main()` function in `main.py` to change:
- Server host/port
- Data directory location
- Authentication settings

## Production Deployment

For production use, consider:
1. Using a proper database (SQLite, PostgreSQL, etc.)
2. Implementing proper authentication (JWT, OAuth)
3. Adding HTTPS support
4. Using a production WSGI server (gunicorn, uwsgi)
5. Adding logging and monitoring
