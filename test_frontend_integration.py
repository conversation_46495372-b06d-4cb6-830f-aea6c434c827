#!/usr/bin/env python3
"""
Test script to verify frontend-backend integration
"""

import json
import base64
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# Configuration
API_BASE_URL = 'http://localhost:8000'
USERNAME = 'demo'
PASSWORD = 'demo123'

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_request(endpoint, method='GET', data=None):
    """Make HTTP request to API"""
    url = f"{API_BASE_URL}{endpoint}"
    
    headers = {
        'Authorization': create_auth_header(),
        'Content-Type': 'application/json'
    }
    
    if data:
        data = json.dumps(data).encode('utf-8')
    
    req = Request(url, data=data, headers=headers, method=method)
    
    try:
        with urlopen(req) as response:
            return json.loads(response.read().decode())
    except HTTPError as e:
        error_body = e.read().decode()
        print(f"HTTP Error {e.code}: {error_body}")
        return None
    except URLError as e:
        print(f"URL Error: {e}")
        return None

def test_create_scenario():
    """Test creating a scenario like the frontend would"""
    print("🧪 Testing scenario creation (like frontend)...")
    
    # Create a scenario similar to what AsIsPage would create
    test_scenario = {
        "name": "As-Is Configuration - Test Policy (POL-123)",
        "policyId": "POL-123",
        "asIsDetails": "Retirement Age: 65, Maturity Age: 121",
        "whatIfOptions": [
            "Face Amount: 500,000",
            "Annual Premium: 5,000"
        ],
        "category": "as-is",
        "data": {
            "policyData": {
                "customerName": "Test Customer",
                "policyNumber": "POL-123"
            },
            "illustrationScenarios": {
                "retirementGoalAge": "65",
                "maturityAge": "121"
            }
        }
    }
    
    # Create the scenario
    result = make_request('/api/scenarios', 'POST', test_scenario)
    if result:
        print(f"✅ Scenario created successfully: {result}")
        
        # Get all scenarios to verify it was saved
        scenarios = make_request('/api/scenarios')
        if scenarios:
            print(f"✅ Found {len(scenarios.get('scenarios', []))} scenarios in database")
            if len(scenarios.get('scenarios', [])) > 0:
                scenario = scenarios['scenarios'][-1]  # Get the last one (newest)
                print(f"   Latest scenario: {scenario['name']} (ID: {scenario['id']})")
                return scenario['id']
        else:
            print("❌ Failed to retrieve scenarios after creation")
    else:
        print("❌ Failed to create scenario")
    
    return None

def test_select_scenario(scenario_id):
    """Test selecting a scenario"""
    print(f"\n🧪 Testing scenario selection...")
    
    # Select the scenario
    result = make_request('/api/scenarios/selected', 'POST', {
        'selectedScenarios': [scenario_id]
    })
    
    if result:
        print(f"✅ Scenario selected successfully: {result}")
        
        # Verify it was selected
        selected = make_request('/api/scenarios/selected')
        if selected and scenario_id in selected.get('selectedScenarios', []):
            print(f"✅ Scenario {scenario_id} is correctly selected")
            return True
        else:
            print(f"❌ Scenario {scenario_id} was not found in selected scenarios")
    else:
        print("❌ Failed to select scenario")
    
    return False

def main():
    """Run integration tests"""
    print("🔗 Frontend-Backend Integration Test")
    print("=" * 50)
    
    # Test scenario creation
    scenario_id = test_create_scenario()
    if not scenario_id:
        print("\n❌ Scenario creation failed - stopping tests")
        return
    
    # Test scenario selection
    if not test_select_scenario(scenario_id):
        print("\n❌ Scenario selection failed")
        return
    
    print("\n🎉 All integration tests passed!")
    print("\n📝 Summary:")
    print("   ✅ Scenario creation works")
    print("   ✅ Scenario selection works")
    print("   ✅ Data persistence works")
    print("\n🚀 Frontend should now work correctly with backend!")

if __name__ == "__main__":
    main()
