import React from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import DashboardOverview from './DashboardOverview';
import PolicySelection from './PolicySelection';
import AsIsPage from './AsIsPage';
import FaceAmountPage from './FaceAmountPage';
import PremiumPage from './PremiumPage';
import IncomePage from './IncomePage';
import LoanRepaymentPage from './LoanRepaymentPage';
import InterestRatePage from './InterestRatePage';
import PolicyLapsePage from './PolicyLapsePage';
import AnalysisReports from './AnalysisReports';
import SelectedScenarios from './SelectedScenarios';
import Settings from './Settings';
import ApiTestPage from '../test/ApiTestPage';

const Dashboard: React.FC = () => {
  const { activeTab } = useDashboard();

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'policy-selection':
        return <PolicySelection />;
      case 'as-is':
        return <AsIsPage />;
      case 'face-amount':
        return <FaceAmountPage />;
      case 'premium':
        return <PremiumPage />;
      case 'income':
        return <IncomePage />;
      case 'loan-repayment':
        return <LoanRepaymentPage />;
      case 'interest-rate':
        return <InterestRatePage />;
      case 'policy-lapse':
        return <PolicyLapsePage />;
      case 'analysis-reports':
        return <AnalysisReports />;
      case 'selected-scenarios':
        return <SelectedScenarios />;
      case 'settings':
        return <Settings />;
      case 'api-test':
        return <ApiTestPage />;
      default:
        return <AsIsPage />;
    }
  };

  return (
    <div className="flex-1 overflow-auto bg-gray-50 dark:bg-[#121212] transition-colors duration-300">
      <div className="p-6 min-h-full">
        {renderActiveTab()}
      </div>
    </div>
  );
};

export default Dashboard;