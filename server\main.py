#!/usr/bin/env python3
"""
Life Insurance Application Backend Server
Using only Python built-in modules for data persistence
"""

import json
import os
import uuid
import hashlib
import base64
from datetime import datetime, timezone
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

class DataStore:
    """Simple JSON file-based data storage using built-in modules only"""
    
    def __init__(self, data_dir="data"):
        self.data_dir = data_dir
        self.ensure_data_directory()
        self.scenarios_file = os.path.join(data_dir, "scenarios.json")
        self.users_file = os.path.join(data_dir, "users.json")
        self.selected_scenarios_file = os.path.join(data_dir, "selected_scenarios.json")
        
    def ensure_data_directory(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_json_file(self, filepath, default=None):
        """Load JSON data from file with error handling"""
        if default is None:
            default = {}
        
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return default
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading {filepath}: {e}")
            return default
    
    def save_json_file(self, filepath, data):
        """Save JSON data to file with error handling"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            return True
        except IOError as e:
            print(f"Error saving {filepath}: {e}")
            return False
    
    def get_scenarios(self, user_id=None):
        """Get all scenarios, optionally filtered by user"""
        scenarios = self.load_json_file(self.scenarios_file, [])
        if user_id:
            return [s for s in scenarios if s.get('userId') == user_id]
        return scenarios
    
    def save_scenario(self, scenario_data, user_id):
        """Save a new scenario"""
        scenarios = self.load_json_file(self.scenarios_file, [])
        
        # Add metadata
        scenario_data['id'] = str(uuid.uuid4())
        scenario_data['userId'] = user_id
        scenario_data['createdAt'] = datetime.now(timezone.utc).isoformat()
        scenario_data['updatedAt'] = datetime.now(timezone.utc).isoformat()
        
        scenarios.append(scenario_data)
        return self.save_json_file(self.scenarios_file, scenarios)
    
    def update_scenario(self, scenario_id, updates, user_id):
        """Update an existing scenario"""
        scenarios = self.load_json_file(self.scenarios_file, [])
        
        for i, scenario in enumerate(scenarios):
            if scenario['id'] == scenario_id and scenario.get('userId') == user_id:
                scenarios[i].update(updates)
                scenarios[i]['updatedAt'] = datetime.now(timezone.utc).isoformat()
                return self.save_json_file(self.scenarios_file, scenarios)
        
        return False
    
    def delete_scenario(self, scenario_id, user_id):
        """Delete a scenario"""
        scenarios = self.load_json_file(self.scenarios_file, [])
        original_length = len(scenarios)
        
        scenarios = [s for s in scenarios if not (s['id'] == scenario_id and s.get('userId') == user_id)]
        
        if len(scenarios) < original_length:
            return self.save_json_file(self.scenarios_file, scenarios)
        return False
    
    def get_selected_scenarios(self, user_id):
        """Get selected scenarios for a user"""
        selected = self.load_json_file(self.selected_scenarios_file, {})
        return selected.get(user_id, [])
    
    def save_selected_scenarios(self, user_id, selected_ids):
        """Save selected scenarios for a user"""
        selected = self.load_json_file(self.selected_scenarios_file, {})
        selected[user_id] = selected_ids
        return self.save_json_file(self.selected_scenarios_file, selected)

class AuthManager:
    """Simple authentication manager using built-in modules"""
    
    def __init__(self, data_store):
        self.data_store = data_store
        # Demo user for testing
        self.ensure_demo_user()
    
    def ensure_demo_user(self):
        """Create demo user if not exists"""
        users = self.data_store.load_json_file(self.data_store.users_file, {})
        if 'demo' not in users:
            users['demo'] = {
                'id': 'demo-user-id',
                'username': 'demo',
                'password_hash': self.hash_password('demo123'),
                'name': 'Demo User',
                'email': '<EMAIL>',
                'created_at': datetime.now(timezone.utc).isoformat()
            }
            self.data_store.save_json_file(self.data_store.users_file, users)
    
    def hash_password(self, password):
        """Hash password using built-in hashlib"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_user(self, username, password):
        """Verify user credentials"""
        users = self.data_store.load_json_file(self.data_store.users_file, {})
        user = users.get(username)
        
        if user and user['password_hash'] == self.hash_password(password):
            return user['id']
        return None
    
    def decode_auth_header(self, auth_header):
        """Decode basic auth header"""
        try:
            if not auth_header.startswith('Basic '):
                return None, None
            
            encoded = auth_header[6:]
            decoded = base64.b64decode(encoded).decode('utf-8')
            username, password = decoded.split(':', 1)
            return username, password
        except:
            return None, None

class LifeInsuranceAPIHandler(BaseHTTPRequestHandler):
    """HTTP request handler for Life Insurance API"""
    
    def __init__(self, *args, data_store=None, auth_manager=None, **kwargs):
        self.data_store = data_store
        self.auth_manager = auth_manager
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        """Send CORS headers"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Access-Control-Max-Age', '86400')
    
    def authenticate_request(self):
        """Authenticate the request and return user_id"""
        auth_header = self.headers.get('Authorization')
        if not auth_header:
            return None
        
        username, password = self.auth_manager.decode_auth_header(auth_header)
        if username and password:
            return self.auth_manager.verify_user(username, password)
        return None
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response with CORS headers"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_cors_headers()
        self.end_headers()
        
        response = json.dumps(data, default=str, ensure_ascii=False)
        self.wfile.write(response.encode('utf-8'))
    
    def send_error_response(self, message, status_code=400):
        """Send error response"""
        self.send_json_response({'error': message}, status_code)
    
    def parse_request_body(self):
        """Parse JSON request body"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                body = self.rfile.read(content_length)
                return json.loads(body.decode('utf-8'))
            return {}
        except (json.JSONDecodeError, ValueError):
            return None
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        query_params = parse_qs(parsed_url.query)
        
        # Health check
        if path == '/health':
            self.send_json_response({'status': 'healthy', 'timestamp': datetime.now().isoformat()})
            return
        
        # Authenticate user
        user_id = self.authenticate_request()
        if not user_id:
            self.send_error_response('Authentication required', 401)
            return
        
        if path == '/api/scenarios':
            scenarios = self.data_store.get_scenarios(user_id)
            self.send_json_response({'scenarios': scenarios})
        
        elif path == '/api/scenarios/selected':
            selected = self.data_store.get_selected_scenarios(user_id)
            self.send_json_response({'selectedScenarios': selected})
        
        else:
            self.send_error_response('Endpoint not found', 404)
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # Authenticate user
        user_id = self.authenticate_request()
        if not user_id:
            self.send_error_response('Authentication required', 401)
            return
        
        # Parse request body
        data = self.parse_request_body()
        if data is None:
            self.send_error_response('Invalid JSON in request body', 400)
            return
        
        if path == '/api/scenarios':
            if self.data_store.save_scenario(data, user_id):
                self.send_json_response({'message': 'Scenario created successfully'}, 201)
            else:
                self.send_error_response('Failed to create scenario', 500)
        
        elif path == '/api/scenarios/selected':
            selected_ids = data.get('selectedScenarios', [])
            if self.data_store.save_selected_scenarios(user_id, selected_ids):
                self.send_json_response({'message': 'Selected scenarios updated successfully'})
            else:
                self.send_error_response('Failed to update selected scenarios', 500)
        
        else:
            self.send_error_response('Endpoint not found', 404)

    def do_PUT(self):
        """Handle PUT requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # Authenticate user
        user_id = self.authenticate_request()
        if not user_id:
            self.send_error_response('Authentication required', 401)
            return

        # Parse request body
        data = self.parse_request_body()
        if data is None:
            self.send_error_response('Invalid JSON in request body', 400)
            return

        # Extract scenario ID from path
        if path.startswith('/api/scenarios/'):
            scenario_id = path.split('/')[-1]
            if scenario_id and scenario_id != 'selected':
                if self.data_store.update_scenario(scenario_id, data, user_id):
                    self.send_json_response({'message': 'Scenario updated successfully'})
                else:
                    self.send_error_response('Scenario not found or update failed', 404)
            else:
                self.send_error_response('Invalid scenario ID', 400)
        else:
            self.send_error_response('Endpoint not found', 404)

    def do_DELETE(self):
        """Handle DELETE requests"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path

        # Authenticate user
        user_id = self.authenticate_request()
        if not user_id:
            self.send_error_response('Authentication required', 401)
            return

        # Extract scenario ID from path
        if path.startswith('/api/scenarios/'):
            scenario_id = path.split('/')[-1]
            if scenario_id and scenario_id != 'selected':
                if self.data_store.delete_scenario(scenario_id, user_id):
                    self.send_json_response({'message': 'Scenario deleted successfully'})
                else:
                    self.send_error_response('Scenario not found or delete failed', 404)
            else:
                self.send_error_response('Invalid scenario ID', 400)
        else:
            self.send_error_response('Endpoint not found', 404)

    def log_message(self, format, *args):
        """Override to customize logging"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def create_handler_class(data_store, auth_manager):
    """Factory function to create handler class with dependencies"""
    class Handler(LifeInsuranceAPIHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, data_store=data_store, auth_manager=auth_manager, **kwargs)
    return Handler

def main():
    """Main server function"""
    # Configuration
    HOST = 'localhost'
    PORT = 8000

    print("=" * 60)
    print("🏥 Life Insurance Application Backend Server")
    print("=" * 60)
    print(f"📊 Using Python built-in modules only")
    print(f"💾 Data storage: JSON files in ./data/")
    print(f"🔐 Authentication: Basic Auth (demo/demo123)")
    print(f"🌐 Server starting on http://{HOST}:{PORT}")
    print("=" * 60)

    # Initialize data store and auth manager
    data_store = DataStore()
    auth_manager = AuthManager(data_store)

    # Create handler class with dependencies
    handler_class = create_handler_class(data_store, auth_manager)

    # Start server
    try:
        server = HTTPServer((HOST, PORT), handler_class)
        print(f"✅ Server running on http://{HOST}:{PORT}")
        print("📋 Available endpoints:")
        print("   GET    /health                    - Health check")
        print("   GET    /api/scenarios             - Get all scenarios")
        print("   POST   /api/scenarios             - Create new scenario")
        print("   PUT    /api/scenarios/{id}        - Update scenario")
        print("   DELETE /api/scenarios/{id}        - Delete scenario")
        print("   GET    /api/scenarios/selected    - Get selected scenarios")
        print("   POST   /api/scenarios/selected    - Update selected scenarios")
        print("\n🔑 Authentication: Basic Auth with username 'demo' and password 'demo123'")
        print("\n⏹️  Press Ctrl+C to stop the server")
        print("=" * 60)

        server.serve_forever()

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
