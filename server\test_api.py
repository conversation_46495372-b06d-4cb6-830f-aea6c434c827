#!/usr/bin/env python3
"""
Simple test script to verify the Life Insurance API is working
"""

import json
import base64
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# Configuration
API_BASE_URL = 'http://localhost:8000'
USERNAME = 'demo'
PASSWORD = 'demo123'

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_request(endpoint, method='GET', data=None):
    """Make HTTP request to API"""
    url = f"{API_BASE_URL}{endpoint}"
    
    headers = {
        'Authorization': create_auth_header(),
        'Content-Type': 'application/json'
    }
    
    if data:
        data = json.dumps(data).encode('utf-8')
    
    req = Request(url, data=data, headers=headers, method=method)
    
    try:
        with urlopen(req) as response:
            return json.loads(response.read().decode())
    except HTTPError as e:
        error_body = e.read().decode()
        print(f"HTTP Error {e.code}: {error_body}")
        return None
    except URLError as e:
        print(f"URL Error: {e}")
        return None

def test_health_check():
    """Test health check endpoint"""
    print("🔍 Testing health check...")
    result = make_request('/health')
    if result:
        print(f"✅ Health check passed: {result}")
        return True
    else:
        print("❌ Health check failed")
        return False

def test_scenarios_crud():
    """Test scenarios CRUD operations"""
    print("\n🔍 Testing scenarios CRUD operations...")
    
    # Test GET scenarios (should be empty initially)
    print("📋 Getting scenarios...")
    scenarios = make_request('/api/scenarios')
    if scenarios is not None:
        print(f"✅ Got scenarios: {len(scenarios.get('scenarios', []))} scenarios found")
    else:
        print("❌ Failed to get scenarios")
        return False
    
    # Test POST scenario (create)
    print("➕ Creating new scenario...")
    test_scenario = {
        "name": "Test Scenario",
        "policyId": "TEST-POL-001",
        "asIsDetails": "Test AS-IS details",
        "whatIfOptions": ["Test option 1", "Test option 2"],
        "category": "premium",
        "data": {"test": "data"}
    }
    
    result = make_request('/api/scenarios', 'POST', test_scenario)
    if result:
        print(f"✅ Scenario created: {result}")
    else:
        print("❌ Failed to create scenario")
        return False
    
    # Test GET scenarios again (should have 1 now)
    print("📋 Getting scenarios after creation...")
    scenarios = make_request('/api/scenarios')
    if scenarios and len(scenarios.get('scenarios', [])) > 0:
        scenario_id = scenarios['scenarios'][0]['id']
        print(f"✅ Found created scenario with ID: {scenario_id}")
        
        # Test PUT scenario (update)
        print("✏️ Updating scenario...")
        update_data = {"name": "Updated Test Scenario"}
        result = make_request(f'/api/scenarios/{scenario_id}', 'PUT', update_data)
        if result:
            print(f"✅ Scenario updated: {result}")
        else:
            print("❌ Failed to update scenario")
            return False
        
        # Test DELETE scenario
        print("🗑️ Deleting scenario...")
        result = make_request(f'/api/scenarios/{scenario_id}', 'DELETE')
        if result:
            print(f"✅ Scenario deleted: {result}")
        else:
            print("❌ Failed to delete scenario")
            return False
    else:
        print("❌ No scenarios found after creation")
        return False
    
    return True

def test_selected_scenarios():
    """Test selected scenarios operations"""
    print("\n🔍 Testing selected scenarios operations...")
    
    # Test GET selected scenarios
    print("📋 Getting selected scenarios...")
    result = make_request('/api/scenarios/selected')
    if result is not None:
        print(f"✅ Got selected scenarios: {result}")
    else:
        print("❌ Failed to get selected scenarios")
        return False
    
    # Test POST selected scenarios (update)
    print("💾 Updating selected scenarios...")
    test_selected = {"selectedScenarios": ["test-id-1", "test-id-2"]}
    result = make_request('/api/scenarios/selected', 'POST', test_selected)
    if result:
        print(f"✅ Selected scenarios updated: {result}")
    else:
        print("❌ Failed to update selected scenarios")
        return False
    
    # Test GET selected scenarios again
    print("📋 Getting selected scenarios after update...")
    result = make_request('/api/scenarios/selected')
    if result and result.get('selectedScenarios') == ["test-id-1", "test-id-2"]:
        print("✅ Selected scenarios correctly saved and retrieved")
    else:
        print("❌ Selected scenarios not correctly saved")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Life Insurance API Test Suite")
    print("=" * 50)
    
    # Test health check first
    if not test_health_check():
        print("\n❌ Health check failed. Make sure the server is running on localhost:8000")
        return
    
    # Test scenarios CRUD
    if not test_scenarios_crud():
        print("\n❌ Scenarios CRUD tests failed")
        return
    
    # Test selected scenarios
    if not test_selected_scenarios():
        print("\n❌ Selected scenarios tests failed")
        return
    
    print("\n🎉 All tests passed! The API is working correctly.")
    print("\n📝 Summary:")
    print("   ✅ Health check")
    print("   ✅ Scenarios CRUD operations")
    print("   ✅ Selected scenarios management")
    print("\n🚀 The backend is ready for frontend integration!")

if __name__ == "__main__":
    main()
