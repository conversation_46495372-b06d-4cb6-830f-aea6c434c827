import React, { useState } from 'react';
import { AlertTriangle, TrendingDown, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const PolicyLapsePage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'No-Lapse Guarantee Analysis',
      type: 'no-lapse',
      triggerAge: '85',
      premiumRequired: '28000',
      riskLevel: 'Low',
      lapseYear: 'Never',
      enabled: true,
    },
    {
      id: '2',
      name: 'Lapse Risk - Current Premiums',
      type: 'current-risk',
      triggerAge: '78',
      premiumRequired: '25000',
      riskLevel: 'Medium',
      lapseYear: '2045',
      enabled: false,
    },
    {
      id: '3',
      name: 'Premium Solve to Prevent Lapse',
      type: 'premium-solve',
      triggerAge: '85',
      premiumRequired: '32000',
      riskLevel: 'Low',
      lapseYear: 'Never',
      enabled: false,
    },
    {
      id: '4',
      name: 'Cash Value Floor Analysis',
      type: 'cash-floor',
      triggerAge: '75',
      premiumRequired: '30000',
      riskLevel: 'High',
      lapseYear: '2042',
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'custom-risk',
    triggerAge: '80',
    premiumAmount: '',
    interestRate: '3.0',
    withdrawalAmount: '0',
  });

  const { addScenario } = useDashboard();

  const currentPremium = 25000;
  const currentCashValue = 75000;
  const currentAge = 45;
  const policyFaceAmount = 500000;

  const lapseTypes = [
    { value: 'no-lapse', label: 'No-Lapse Guarantee' },
    { value: 'current-risk', label: 'Current Lapse Risk' },
    { value: 'premium-solve', label: 'Premium Solve' },
    { value: 'cash-floor', label: 'Cash Value Floor' },
    { value: 'custom-risk', label: 'Custom Risk Analysis' },
  ];

  const riskLevels = [
    { value: 'Low', color: 'text-green-600', bg: 'bg-green-100' },
    { value: 'Medium', color: 'text-yellow-600', bg: 'bg-yellow-100' },
    { value: 'High', color: 'text-red-600', bg: 'bg-red-100' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateLapseRisk = (premiumAmount: string, interestRate: string, withdrawalAmount: string) => {
    const premium = parseFloat(premiumAmount) || currentPremium;
    const rate = parseFloat(interestRate) || 5.5;
    const withdrawal = parseFloat(withdrawalAmount) || 0;

    // Simplified lapse risk calculation
    const riskScore = (premium / currentPremium) * (rate / 5.5) - (withdrawal / 10000);
    
    if (riskScore >= 1.2) return 'Low';
    if (riskScore >= 0.8) return 'Medium';
    return 'High';
  };

  const calculateLapseYear = (riskLevel: string, triggerAge: string) => {
    const age = parseInt(triggerAge);
    const currentYear = new Date().getFullYear();
    const yearsToTrigger = age - currentAge;

    if (riskLevel === 'Low') return 'Never';
    if (riskLevel === 'Medium') return (currentYear + yearsToTrigger + 5).toString();
    return (currentYear + yearsToTrigger - 5).toString();
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.premiumAmount) {
      alert('Please fill in all required fields for the custom scenario');
      return;
    }

    const riskLevel = calculateLapseRisk(
      customScenario.premiumAmount,
      customScenario.interestRate,
      customScenario.withdrawalAmount
    );

    const lapseYear = calculateLapseYear(riskLevel, customScenario.triggerAge);

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      triggerAge: customScenario.triggerAge,
      premiumRequired: customScenario.premiumAmount,
      riskLevel,
      lapseYear,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({
      name: '',
      type: 'custom-risk',
      triggerAge: '80',
      premiumAmount: '',
      interestRate: '3.0',
      withdrawalAmount: '0',
    });
  };

  const saveSelectedScenarios = async () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);

    if (selectedScenarios.length === 0) {
      alert('Please select at least one scenario to save');
      return;
    }

    try {
      for (const scenario of selectedScenarios) {
        const newScenario = {
          id: Date.now().toString() + Math.random(),
          name: `Policy Lapse: ${scenario.name}`,
          policyId: 'POL-2024-001',
          asIsDetails: 'Current Policy Details',
          whatIfOptions: [`Policy Lapse: ${scenario.name} (${scenario.riskLevel} Risk - Age ${scenario.triggerAge})`],
          category: 'policy-lapse' as const,
          data: {
            lapseType: scenario.type,
            triggerAge: parseInt(scenario.triggerAge),
            premiumRequired: parseFloat(scenario.premiumRequired),
            riskLevel: scenario.riskLevel,
            lapseYear: scenario.lapseYear,
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await addScenario(newScenario);
      }

      alert(`${selectedScenarios.length} Policy Lapse scenarios saved successfully!`);
    } catch (error) {
      alert('Error saving policy lapse scenarios. Please try again.');
      console.error('Error saving policy lapse scenarios:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Policy Lapse Analysis</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze lapse risk scenarios and strategies to prevent policy termination.</p>
      </div>

      {/* Current Policy Risk */}
      <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Lapse Risk Assessment</h3>
          <AlertTriangle className="w-6 h-6 text-yellow-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Premium</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              ${currentPremium.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Cash Value</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${currentCashValue.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Risk Level</p>
            <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              Medium
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Projected Lapse Year</p>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">
              2045
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Lapse Prevention Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => {
            const riskConfig = riskLevels.find(r => r.value === scenario.riskLevel);
            return (
              <div
                key={scenario.id}
                className={`p-4 border rounded-lg transition-all ${
                  scenario.enabled
                    ? 'border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-700'
                    : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={scenario.enabled}
                      onChange={() => handleScenarioToggle(scenario.id)}
                      className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                    />
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${riskConfig?.bg} ${riskConfig?.color}`}>
                      {scenario.riskLevel} Risk
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">Trigger Age</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {scenario.triggerAge} years
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">Premium Required</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      ${parseFloat(scenario.premiumRequired).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">Lapse Year</p>
                    <p className={`font-semibold ${scenario.lapseYear === 'Never' ? 'text-green-600' : 'text-red-600'}`}>
                      {scenario.lapseYear}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">Strategy Type</p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                      {scenario.type.replace('-', ' ')}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Lapse Analysis</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Scenario Name"
              value={customScenario.name}
              onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
              placeholder="Enter scenario name"
            />
            <Select
              label="Analysis Type"
              value={customScenario.type}
              onChange={(e) => handleCustomScenarioChange('type', e.target.value)}
              options={lapseTypes}
            />
            <Input
              label="Trigger Age"
              type="number"
              value={customScenario.triggerAge}
              onChange={(e) => handleCustomScenarioChange('triggerAge', e.target.value)}
              placeholder="Age when lapse risk increases"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              label="Premium Amount ($)"
              type="number"
              value={customScenario.premiumAmount}
              onChange={(e) => handleCustomScenarioChange('premiumAmount', e.target.value)}
              placeholder="Required premium"
            />
            <Input
              label="Interest Rate (%)"
              type="number"
              step="0.1"
              value={customScenario.interestRate}
              onChange={(e) => handleCustomScenarioChange('interestRate', e.target.value)}
              placeholder="Assumed interest rate"
            />
            <Input
              label="Annual Withdrawal ($)"
              type="number"
              value={customScenario.withdrawalAmount}
              onChange={(e) => handleCustomScenarioChange('withdrawalAmount', e.target.value)}
              placeholder="Annual withdrawal amount"
            />
            <div className="flex items-end">
              <Button onClick={addCustomScenario} className="w-full">
                Add Scenario
              </Button>
            </div>
          </div>
        </div>
        
        {customScenario.premiumAmount && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mt-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">Risk Level</p>
                <p className={`font-semibold ${
                  calculateLapseRisk(customScenario.premiumAmount, customScenario.interestRate, customScenario.withdrawalAmount) === 'Low' ? 'text-green-600' :
                  calculateLapseRisk(customScenario.premiumAmount, customScenario.interestRate, customScenario.withdrawalAmount) === 'Medium' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {calculateLapseRisk(customScenario.premiumAmount, customScenario.interestRate, customScenario.withdrawalAmount)}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Projected Lapse Year</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  {calculateLapseYear(
                    calculateLapseRisk(customScenario.premiumAmount, customScenario.interestRate, customScenario.withdrawalAmount),
                    customScenario.triggerAge
                  )}
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Lapse Prevention Strategies */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Lapse Prevention Strategies</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100">Premium Solve</h4>
            <p className="text-sm text-green-700 dark:text-green-300 mt-1">Calculate minimum premium to prevent lapse</p>
          </div>
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100">No-Lapse Guarantee</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Ensure policy remains in force regardless of performance</p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default PolicyLapsePage;