#!/usr/bin/env python3
"""
Debug frontend display issue - check what data the frontend should see
"""

import json
import base64
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# Configuration
API_BASE_URL = 'http://localhost:8000'
USERNAME = 'demo'
PASSWORD = 'demo123'

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_request(endpoint, method='GET', data=None):
    """Make HTTP request to API"""
    url = f"{API_BASE_URL}{endpoint}"
    
    headers = {
        'Authorization': create_auth_header(),
        'Content-Type': 'application/json'
    }
    
    if data:
        data = json.dumps(data).encode('utf-8')
    
    req = Request(url, data=data, headers=headers, method=method)
    
    try:
        with urlopen(req) as response:
            return json.loads(response.read().decode())
    except HTTPError as e:
        error_body = e.read().decode()
        print(f"HTTP Error {e.code}: {error_body}")
        return None
    except URLError as e:
        print(f"URL Error: {e}")
        return None

def debug_frontend_data():
    """Debug exactly what the frontend should see"""
    print("🔍 DEBUGGING FRONTEND DATA DISPLAY")
    print("=" * 60)
    
    print("📡 Step 1: Simulating frontend initial data load...")
    
    # Get data exactly like DashboardContext does
    scenarios_response = make_request('/api/scenarios')
    selected_response = make_request('/api/scenarios/selected')
    
    if not scenarios_response or not selected_response:
        print("❌ Failed to get data from backend")
        return False
    
    all_scenarios = scenarios_response['scenarios']
    selected_ids = selected_response['selectedScenarios']
    
    print(f"✅ Loaded {len(all_scenarios)} scenarios from backend")
    print(f"✅ Loaded {len(selected_ids)} selected scenario IDs")
    
    print("\n📋 Step 2: Raw data from backend...")
    print("All Scenarios:")
    for i, scenario in enumerate(all_scenarios, 1):
        print(f"  {i}. {scenario['name']}")
        print(f"     ID: {scenario['id']}")
        print(f"     Category: {scenario['category']}")
        print(f"     Policy: {scenario['policyId']}")
        print()
    
    print("Selected Scenario IDs:")
    for i, selected_id in enumerate(selected_ids, 1):
        print(f"  {i}. {selected_id}")
    
    print("\n🔍 Step 3: Simulating SelectedScenarios component filtering...")
    
    # This is exactly what SelectedScenarios.tsx does:
    filtered_scenarios = [
        scenario for scenario in all_scenarios 
        if scenario['id'] in selected_ids
    ]
    
    print(f"📊 Filtered scenarios (what should appear): {len(filtered_scenarios)}")
    
    if filtered_scenarios:
        print("\n✅ Scenarios that SHOULD be visible in Selected Scenarios tab:")
        for i, scenario in enumerate(filtered_scenarios, 1):
            print(f"  {i}. ✅ {scenario['name']}")
            print(f"     Category: {scenario['category']}")
            print(f"     Policy: {scenario['policyId']}")
            print(f"     What-If: {', '.join(scenario['whatIfOptions'])}")
            print(f"     ID: {scenario['id']}")
            print(f"     Created: {scenario['createdAt']}")
            print()
        
        print("🎉 CONCLUSION: Frontend should display these scenarios!")
        print("If you don't see them, the issue is in React rendering.")
        return True
    else:
        print("❌ No scenarios would be displayed!")
        print("This means the filtering logic has an issue.")
        
        # Debug the filtering
        print("\n🔍 Debugging filtering logic...")
        print("Checking each scenario:")
        for scenario in all_scenarios:
            is_selected = scenario['id'] in selected_ids
            print(f"  Scenario: {scenario['name']}")
            print(f"  ID: {scenario['id']}")
            print(f"  Is in selected_ids: {is_selected}")
            if not is_selected:
                print(f"  ❌ This scenario is NOT selected")
            else:
                print(f"  ✅ This scenario IS selected")
            print()
        
        return False

def test_browser_api_calls():
    """Test what happens when browser makes API calls"""
    print("\n🌐 Step 4: Testing browser API calls...")
    print("=" * 60)
    
    print("Testing GET /api/scenarios (what browser calls on page load)...")
    scenarios = make_request('/api/scenarios')
    if scenarios:
        print(f"✅ Browser would receive {len(scenarios['scenarios'])} scenarios")
    else:
        print("❌ Browser API call failed")
        return False
    
    print("\nTesting GET /api/scenarios/selected (what browser calls on page load)...")
    selected = make_request('/api/scenarios/selected')
    if selected:
        print(f"✅ Browser would receive {len(selected['selectedScenarios'])} selected IDs")
    else:
        print("❌ Browser API call failed")
        return False
    
    print("\n📊 Browser would have this data:")
    print(f"  scenarios: {len(scenarios['scenarios'])} items")
    print(f"  selectedScenarios: {len(selected['selectedScenarios'])} items")
    
    return True

def check_data_consistency():
    """Check if there are any data consistency issues"""
    print("\n🔍 Step 5: Checking data consistency...")
    print("=" * 60)
    
    scenarios = make_request('/api/scenarios')
    selected = make_request('/api/scenarios/selected')
    
    if not scenarios or not selected:
        print("❌ Failed to get data")
        return False
    
    all_scenario_ids = [s['id'] for s in scenarios['scenarios']]
    selected_ids = selected['selectedScenarios']
    
    print("Checking for orphaned selected IDs...")
    orphaned_ids = [sid for sid in selected_ids if sid not in all_scenario_ids]
    
    if orphaned_ids:
        print(f"⚠️  Found {len(orphaned_ids)} orphaned selected IDs:")
        for oid in orphaned_ids:
            print(f"  - {oid} (selected but scenario doesn't exist)")
    else:
        print("✅ No orphaned selected IDs found")
    
    print("\nChecking scenario-selection mapping...")
    for scenario in scenarios['scenarios']:
        is_selected = scenario['id'] in selected_ids
        status = "✅ SELECTED" if is_selected else "❌ NOT SELECTED"
        print(f"  {scenario['name']}: {status}")
    
    return len(orphaned_ids) == 0

def main():
    """Run complete frontend debugging"""
    print("🐛 FRONTEND DISPLAY DEBUGGING")
    print("Checking why scenarios might not appear in Selected Scenarios tab")
    print()
    
    # Debug frontend data
    if not debug_frontend_data():
        print("\n❌ Frontend data debugging failed")
        return
    
    # Test browser API calls
    if not test_browser_api_calls():
        print("\n❌ Browser API testing failed")
        return
    
    # Check data consistency
    if not check_data_consistency():
        print("\n❌ Data consistency check failed")
        return
    
    print("\n🎯 DEBUGGING SUMMARY:")
    print("=" * 60)
    print("✅ Backend is working correctly")
    print("✅ Data is properly stored and structured")
    print("✅ API endpoints are responding correctly")
    print("✅ Scenarios are created and selected properly")
    print("✅ Frontend should receive correct data")
    
    print("\n🔍 IF SCENARIOS ARE NOT SHOWING IN BROWSER:")
    print("The issue is in the React frontend rendering, not the backend.")
    print()
    print("🛠️  DEBUGGING STEPS FOR BROWSER:")
    print("1. Open http://localhost:5174 in browser")
    print("2. Open Developer Tools (F12)")
    print("3. Go to Console tab")
    print("4. Go to Selected Scenarios tab")
    print("5. Check for any JavaScript errors")
    print("6. Go to Network tab")
    print("7. Refresh page and check if API calls are successful")
    print("8. Look for any failed requests (red entries)")
    
    print("\n📊 EXPECTED BEHAVIOR:")
    print("- Counter should show '3 scenario(s) selected'")
    print("- 3 scenarios should be listed with checkboxes checked")
    print("- Each scenario should show name, category, policy ID")
    
    print("\n🔧 IF STILL NOT WORKING:")
    print("The issue might be in React component state management")
    print("or the DashboardContext data loading logic.")

if __name__ == "__main__":
    main()
