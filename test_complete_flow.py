#!/usr/bin/env python3
"""
Complete flow test to verify the entire system works end-to-end
"""

import json
import base64
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError
import time

# Configuration
API_BASE_URL = 'http://localhost:8000'
USERNAME = 'demo'
PASSWORD = 'demo123'

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_request(endpoint, method='GET', data=None):
    """Make HTTP request to API"""
    url = f"{API_BASE_URL}{endpoint}"
    
    headers = {
        'Authorization': create_auth_header(),
        'Content-Type': 'application/json'
    }
    
    if data:
        data = json.dumps(data).encode('utf-8')
    
    req = Request(url, data=data, headers=headers, method=method)
    
    try:
        with urlopen(req) as response:
            return json.loads(response.read().decode())
    except HTTPError as e:
        error_body = e.read().decode()
        print(f"HTTP Error {e.code}: {error_body}")
        return None
    except URLError as e:
        print(f"URL Error: {e}")
        return None

def clear_all_data():
    """Clear all existing data for clean test"""
    print("🧹 Clearing existing data...")
    
    # Get all scenarios
    scenarios = make_request('/api/scenarios')
    if scenarios and scenarios.get('scenarios'):
        for scenario in scenarios['scenarios']:
            result = make_request(f'/api/scenarios/{scenario["id"]}', 'DELETE')
            if result:
                print(f"   Deleted scenario: {scenario['name']}")
    
    # Clear selected scenarios
    make_request('/api/scenarios/selected', 'POST', {'selectedScenarios': []})
    print("   Cleared selected scenarios")

def test_scenario_creation():
    """Test creating scenarios like different frontend components would"""
    print("\n📝 Testing scenario creation from different components...")
    
    scenarios_to_create = [
        {
            "name": "As-Is Configuration - Test Customer (POL-001)",
            "policyId": "POL-001",
            "asIsDetails": "Retirement Age: 65, Maturity Age: 121",
            "whatIfOptions": ["Face Amount: 500,000", "Annual Premium: 5,000"],
            "category": "as-is",
            "data": {"source": "AsIsPage"}
        },
        {
            "name": "Face Amount Scenario - Test Customer",
            "policyId": "POL-001",
            "asIsDetails": "Current Death Benefit: $500,000",
            "whatIfOptions": ["Increase to $750,000"],
            "category": "face-amount",
            "data": {"source": "FaceAmountPage"}
        },
        {
            "name": "Income: Retirement Withdrawal",
            "policyId": "POL-001",
            "asIsDetails": "Current Policy Details",
            "whatIfOptions": ["Income: Retirement Withdrawal (withdrawal - $50,000 annual)"],
            "category": "income",
            "data": {"source": "IncomePage"}
        },
        {
            "name": "Loan Repayment: Full Payment",
            "policyId": "POL-001",
            "asIsDetails": "Current Policy Details",
            "whatIfOptions": ["Loan Repayment: Full Payment ($25,000 - immediate)"],
            "category": "loan-repayment",
            "data": {"source": "LoanRepaymentPage"}
        }
    ]
    
    created_ids = []
    for scenario_data in scenarios_to_create:
        result = make_request('/api/scenarios', 'POST', scenario_data)
        if result:
            print(f"   ✅ Created: {scenario_data['name']}")
            # Get the created scenario to get its ID
            scenarios = make_request('/api/scenarios')
            if scenarios:
                latest = scenarios['scenarios'][-1]
                created_ids.append(latest['id'])
        else:
            print(f"   ❌ Failed to create: {scenario_data['name']}")
            return []
    
    return created_ids

def test_scenario_selection(scenario_ids):
    """Test selecting scenarios"""
    print(f"\n✅ Testing scenario selection...")
    
    # Select first 2 scenarios
    selected_ids = scenario_ids[:2]
    result = make_request('/api/scenarios/selected', 'POST', {
        'selectedScenarios': selected_ids
    })
    
    if result:
        print(f"   ✅ Selected {len(selected_ids)} scenarios")
        
        # Verify selection
        selected = make_request('/api/scenarios/selected')
        if selected and set(selected['selectedScenarios']) == set(selected_ids):
            print(f"   ✅ Selection verified: {selected['selectedScenarios']}")
            return True
        else:
            print(f"   ❌ Selection mismatch: expected {selected_ids}, got {selected}")
    else:
        print("   ❌ Failed to select scenarios")
    
    return False

def test_data_persistence():
    """Test that data persists across 'sessions' (simulated by clearing and reloading)"""
    print(f"\n💾 Testing data persistence...")
    
    # Get current state
    scenarios_before = make_request('/api/scenarios')
    selected_before = make_request('/api/scenarios/selected')
    
    if not scenarios_before or not selected_before:
        print("   ❌ Failed to get current state")
        return False
    
    scenario_count = len(scenarios_before['scenarios'])
    selected_count = len(selected_before['selectedScenarios'])
    
    print(f"   📊 Current state: {scenario_count} scenarios, {selected_count} selected")
    
    # Simulate page refresh by getting data again
    time.sleep(1)
    scenarios_after = make_request('/api/scenarios')
    selected_after = make_request('/api/scenarios/selected')
    
    if not scenarios_after or not selected_after:
        print("   ❌ Failed to get state after 'refresh'")
        return False
    
    # Verify data is the same
    if (len(scenarios_after['scenarios']) == scenario_count and 
        len(selected_after['selectedScenarios']) == selected_count):
        print(f"   ✅ Data persisted correctly after 'refresh'")
        return True
    else:
        print(f"   ❌ Data changed after 'refresh'")
        return False

def test_scenario_operations(scenario_ids):
    """Test updating and deleting scenarios"""
    print(f"\n🔧 Testing scenario operations...")
    
    if not scenario_ids:
        print("   ❌ No scenarios to test with")
        return False
    
    # Test update
    test_id = scenario_ids[0]
    update_data = {"name": "Updated Test Scenario"}
    result = make_request(f'/api/scenarios/{test_id}', 'PUT', update_data)
    
    if result:
        print(f"   ✅ Updated scenario {test_id}")
        
        # Verify update
        scenarios = make_request('/api/scenarios')
        if scenarios:
            updated_scenario = next((s for s in scenarios['scenarios'] if s['id'] == test_id), None)
            if updated_scenario and updated_scenario['name'] == "Updated Test Scenario":
                print(f"   ✅ Update verified")
            else:
                print(f"   ❌ Update not reflected")
                return False
    else:
        print(f"   ❌ Failed to update scenario")
        return False
    
    # Test delete
    delete_id = scenario_ids[-1]
    result = make_request(f'/api/scenarios/{delete_id}', 'DELETE')
    
    if result:
        print(f"   ✅ Deleted scenario {delete_id}")
        
        # Verify deletion
        scenarios = make_request('/api/scenarios')
        if scenarios:
            deleted_scenario = next((s for s in scenarios['scenarios'] if s['id'] == delete_id), None)
            if not deleted_scenario:
                print(f"   ✅ Deletion verified")
                return True
            else:
                print(f"   ❌ Scenario still exists after deletion")
    else:
        print(f"   ❌ Failed to delete scenario")
    
    return False

def main():
    """Run complete flow test"""
    print("🔄 Complete Flow Test - Life Insurance Application")
    print("=" * 60)
    
    # Step 1: Clear existing data
    clear_all_data()
    
    # Step 2: Test scenario creation
    scenario_ids = test_scenario_creation()
    if not scenario_ids:
        print("\n❌ Scenario creation failed - stopping tests")
        return
    
    # Step 3: Test scenario selection
    if not test_scenario_selection(scenario_ids):
        print("\n❌ Scenario selection failed")
        return
    
    # Step 4: Test data persistence
    if not test_data_persistence():
        print("\n❌ Data persistence failed")
        return
    
    # Step 5: Test scenario operations
    if not test_scenario_operations(scenario_ids):
        print("\n❌ Scenario operations failed")
        return
    
    print("\n🎉 Complete flow test PASSED!")
    print("\n📋 Summary:")
    print("   ✅ Scenario creation from multiple components")
    print("   ✅ Scenario selection and persistence")
    print("   ✅ Data persistence across sessions")
    print("   ✅ Scenario update and delete operations")
    print("\n🚀 The system is working correctly end-to-end!")
    print("\n💡 You can now:")
    print("   1. Open http://localhost:5174 in your browser")
    print("   2. Navigate to different tabs and create scenarios")
    print("   3. Go to 'Selected Scenarios' tab to see your saved scenarios")
    print("   4. Refresh the page - your data will persist!")
    print("   5. Use the 'API Test' tab to run frontend integration tests")

if __name__ == "__main__":
    main()
