#!/usr/bin/env python3
"""
Test the exact frontend flow you described:
1. User selects scenarios in illustration tabs
2. User clicks save button
3. Check if scenarios appear in Selected Scenarios tab
"""

import json
import base64
from urllib.request import Request, urlopen
from urllib.error import HTTPError, URLError

# Configuration
API_BASE_URL = 'http://localhost:8000'
USERNAME = 'demo'
PASSWORD = 'demo123'

def create_auth_header():
    """Create Basic Auth header"""
    credentials = f"{USERNAME}:{PASSWORD}"
    encoded = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded}"

def make_request(endpoint, method='GET', data=None):
    """Make HTTP request to API"""
    url = f"{API_BASE_URL}{endpoint}"
    
    headers = {
        'Authorization': create_auth_header(),
        'Content-Type': 'application/json'
    }
    
    if data:
        data = json.dumps(data).encode('utf-8')
    
    req = Request(url, data=data, headers=headers, method=method)
    
    try:
        with urlopen(req) as response:
            return json.loads(response.read().decode())
    except HTTPError as e:
        error_body = e.read().decode()
        print(f"HTTP Error {e.code}: {error_body}")
        return None
    except URLError as e:
        print(f"URL Error: {e}")
        return None

def clear_all_data():
    """Clear all existing data"""
    print("🧹 Clearing all data...")
    
    # Get all scenarios
    scenarios = make_request('/api/scenarios')
    if scenarios and scenarios.get('scenarios'):
        for scenario in scenarios['scenarios']:
            make_request(f'/api/scenarios/{scenario["id"]}', 'DELETE')
    
    # Clear selected scenarios
    make_request('/api/scenarios/selected', 'POST', {'selectedScenarios': []})
    print("   ✅ All data cleared")

def test_income_tab_flow():
    """Test the exact Income tab flow"""
    print("\n💰 Testing Income Tab Flow (Your Exact Use Case)")
    print("=" * 60)
    
    print("👤 Step 1: User goes to Income tab")
    print("📝 Step 2: User creates income scenarios")
    print("✅ Step 3: User selects scenarios (checks boxes)")
    print("🖱️  Step 4: User clicks 'Save Selected Scenarios'")
    
    # Simulate creating income scenarios (like frontend would do)
    income_scenarios = [
        {
            "name": "Income: Retirement Withdrawal",
            "policyId": "POL-2024-001",
            "asIsDetails": "Current Policy Details",
            "whatIfOptions": ["Income: Retirement Withdrawal (withdrawal - $50,000 annual)"],
            "category": "income",
            "data": {
                "incomeType": "withdrawal",
                "amount": 50000,
                "frequency": "annual",
                "startAge": 65,
                "endAge": 85,
                "cashValueImpact": "decrease",
                "interestRate": 4.5,
                "repaymentPlan": "none"
            }
        },
        {
            "name": "Income: Policy Loan",
            "policyId": "POL-2024-001",
            "asIsDetails": "Current Policy Details",
            "whatIfOptions": ["Income: Policy Loan (loan - $25,000 annual)"],
            "category": "income",
            "data": {
                "incomeType": "loan",
                "amount": 25000,
                "frequency": "annual",
                "startAge": 60,
                "endAge": 70,
                "cashValueImpact": "decrease",
                "interestRate": 5.0,
                "repaymentPlan": "interest-only"
            }
        }
    ]
    
    created_scenario_ids = []
    
    print("\n📋 Creating scenarios (simulating frontend addScenario calls)...")
    for i, scenario in enumerate(income_scenarios, 1):
        print(f"   Creating scenario {i}: {scenario['name']}")
        
        # Create scenario
        result = make_request('/api/scenarios', 'POST', scenario)
        if result:
            print(f"   ✅ Scenario {i} created successfully")
            
            # Get the created scenario ID (simulating frontend auto-selection)
            scenarios = make_request('/api/scenarios')
            if scenarios and scenarios.get('scenarios'):
                latest_scenario = scenarios['scenarios'][-1]
                scenario_id = latest_scenario['id']
                created_scenario_ids.append(scenario_id)
                
                # Auto-select the scenario (simulating frontend auto-selection)
                current_selected = make_request('/api/scenarios/selected')
                current_ids = current_selected['selectedScenarios'] if current_selected else []
                new_selected_ids = current_ids + [scenario_id]
                
                select_result = make_request('/api/scenarios/selected', 'POST', {
                    'selectedScenarios': new_selected_ids
                })
                
                if select_result:
                    print(f"   ✅ Scenario {i} auto-selected")
                else:
                    print(f"   ❌ Failed to auto-select scenario {i}")
        else:
            print(f"   ❌ Failed to create scenario {i}")
    
    return created_scenario_ids

def verify_selected_scenarios_tab():
    """Verify what appears in Selected Scenarios tab"""
    print("\n📊 Step 5: Checking Selected Scenarios Tab...")
    print("=" * 60)
    
    # Get all scenarios
    all_scenarios = make_request('/api/scenarios')
    if not all_scenarios:
        print("   ❌ Failed to get scenarios")
        return False
    
    # Get selected scenarios
    selected_scenarios = make_request('/api/scenarios/selected')
    if not selected_scenarios:
        print("   ❌ Failed to get selected scenarios")
        return False
    
    total_scenarios = len(all_scenarios['scenarios'])
    selected_ids = selected_scenarios['selectedScenarios']
    total_selected = len(selected_ids)
    
    print(f"   📈 Total scenarios in database: {total_scenarios}")
    print(f"   ✅ Total scenarios selected: {total_selected}")
    
    if total_selected > 0:
        print("\n   📋 Scenarios that should appear in Selected Scenarios tab:")
        for scenario in all_scenarios['scenarios']:
            if scenario['id'] in selected_ids:
                print(f"   ✅ {scenario['name']}")
                print(f"      ID: {scenario['id']}")
                print(f"      Category: {scenario['category']}")
                print(f"      Created: {scenario['createdAt']}")
        
        print(f"\n   🎉 SUCCESS: {total_selected} scenarios should be visible in Selected Scenarios tab!")
        print("   💡 If you don't see them in the frontend, the issue is in the React components.")
        return True
    else:
        print("   ❌ No scenarios are selected!")
        print("   💡 This means the auto-selection logic is not working.")
        return False

def test_frontend_data_loading():
    """Test what the frontend should see when loading data"""
    print("\n🔄 Step 6: Testing Frontend Data Loading...")
    print("=" * 60)
    
    print("   📡 Simulating frontend initial data load...")
    
    # Simulate what DashboardContext does on mount
    scenarios = make_request('/api/scenarios')
    selected_scenarios = make_request('/api/scenarios/selected')
    
    if scenarios and selected_scenarios:
        print(f"   ✅ Frontend would load {len(scenarios['scenarios'])} scenarios")
        print(f"   ✅ Frontend would load {len(selected_scenarios['selectedScenarios'])} selected scenarios")
        
        print("\n   📋 Data that frontend receives:")
        print("   Scenarios:")
        for scenario in scenarios['scenarios']:
            print(f"     - {scenario['name']} (ID: {scenario['id']})")
        
        print("   Selected IDs:")
        for selected_id in selected_scenarios['selectedScenarios']:
            print(f"     - {selected_id}")
        
        # Check if selected IDs match scenario IDs
        scenario_ids = [s['id'] for s in scenarios['scenarios']]
        selected_ids = selected_scenarios['selectedScenarios']
        
        valid_selections = [sid for sid in selected_ids if sid in scenario_ids]
        invalid_selections = [sid for sid in selected_ids if sid not in scenario_ids]
        
        print(f"\n   ✅ Valid selections: {len(valid_selections)}")
        if invalid_selections:
            print(f"   ⚠️  Invalid selections: {len(invalid_selections)}")
            for invalid_id in invalid_selections:
                print(f"      - {invalid_id} (scenario not found)")
        
        return len(valid_selections) > 0
    else:
        print("   ❌ Failed to load data")
        return False

def main():
    """Run the complete frontend flow test"""
    print("🧪 FRONTEND FLOW TEST")
    print("Testing the exact user workflow you described")
    print()
    
    # Clear existing data for clean test
    clear_all_data()
    
    # Test the Income tab flow
    created_ids = test_income_tab_flow()
    
    if not created_ids:
        print("\n❌ No scenarios were created - test failed")
        return
    
    # Verify Selected Scenarios tab
    if not verify_selected_scenarios_tab():
        print("\n❌ Selected Scenarios tab verification failed")
        return
    
    # Test frontend data loading
    if not test_frontend_data_loading():
        print("\n❌ Frontend data loading test failed")
        return
    
    print("\n🎉 BACKEND TEST PASSED!")
    print("\n✅ What this confirms:")
    print("   • Backend is working correctly")
    print("   • Scenarios are being created")
    print("   • Scenarios are being auto-selected")
    print("   • Data is persisting in backend")
    print("   • API endpoints are responding correctly")
    
    print("\n🔍 If scenarios are not showing in frontend:")
    print("   1. Check browser console for JavaScript errors")
    print("   2. Check Network tab for failed API calls")
    print("   3. Verify DashboardContext is loading data correctly")
    print("   4. Check SelectedScenarios component rendering")
    
    print("\n🚀 Next steps:")
    print("   1. Open http://localhost:5174 in browser")
    print("   2. Open browser developer tools (F12)")
    print("   3. Go to Income tab and create scenarios")
    print("   4. Check Selected Scenarios tab")
    print("   5. Look for any errors in Console tab")

if __name__ == "__main__":
    main()
