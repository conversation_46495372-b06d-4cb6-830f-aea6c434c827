import React, { useState } from 'react';
import { ArrowR<PERSON>, Calculator, Search, User, Shield, DollarSign, FileText, CreditCard, Activity } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

// TypeScript interfaces
interface Policy {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
}

interface CustomerDetails {
  DOB: string;
  Email: string;
  Phone: string;
  Address: string;
  Occupation: string;
  "Annual Income": string;
  "Customer ID": string;
  "Policy Number": string;
  "Policy Type": string;
  Status: string;
  "Available Policies": Policy[];
}

type CustomerDataMap = {
  [key: string]: CustomerDetails;
};

interface CurrentCustomerData {
  name: string;
  policyNumber: string;
  customerId: string;
  details: CustomerDetails;
}

const PolicySelection = () => {
  const [customerName, setCustomerName] = useState('');
  const [policyNumber, setPolicyNumber] = useState('');
  const [customerId, setCustomerId] = useState('');
  const [searchClicked, setSearchClicked] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [currentCustomerData, setCurrentCustomerData] = useState<CurrentCustomerData | null>(null);
  const [selectedPolicyIndex, setSelectedPolicyIndex] = useState<number | null>(null);
  const { setActiveTab, setSelectedCustomerData, setSelectedPolicyData } = useDashboard();

  // Helper: localStorage key
  const getStorageKey = (id: string, policy: string, name: string) => `customerData:${id.trim()}|${policy.trim()}|${name.trim()}`;

  // Save details to localStorage on successful search
  const saveToLocalStorage = (id: string, policy: string, name: string) => {
    const key = getStorageKey(id, policy, name);
    localStorage.setItem(key, JSON.stringify({ id, policy, name }));
  };

  // Try to auto-fill other fields if a match is found in localStorage
  const tryAutoFill = (changedField: 'id' | 'policy' | 'name', value: string) => {
    // Only auto-fill if the other fields are empty
    let found = false;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('customerData:')) {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        if (
          (changedField === 'id' && data.id === value && !policyNumber && !customerName) ||
          (changedField === 'policy' && data.policy === value && !customerId && !customerName) ||
          (changedField === 'name' && data.name === value && !customerId && !policyNumber)
        ) {
          if (changedField !== 'id') setCustomerId(data.id);
          if (changedField !== 'policy') setPolicyNumber(data.policy);
          if (changedField !== 'name') setCustomerName(data.name);
          found = true;
          break;
        }
      }
    }
    return found;
  };

  // Add this helper inside your component
  const findCustomerById = (id: string) => {
    const entry = Object.keys(customerData).find(key => key.startsWith(`${id.trim()}|`));
    if (entry) {
      const [, policy, name] = entry.split('|');
      return { policy, name };
    }
    return null;
  };

  // Enhanced customer data with multiple policies per customer (same as Streamlit)
  const customerData: CustomerDataMap = {
    "CUS-567890|POL-12345678|John Smith": {
      DOB: "05.02.1994",
      Email: "<EMAIL>",
      Phone: "(*************",
      Address: "123 Main Street, New York, NY 10001",
      Occupation: "Software Engineer",
      "Annual Income": "$85,000",
      "Customer ID": "CUS-567890",
      "Policy Number": "POL-12345678",
      "Policy Type": "Whole Life Insurance",
      Status: "Active",
      "Available Policies": [
        {
          id: "POL-12345678",
          name: "Whole Life Insurance",
          description: "Permanent life insurance with cash value accumulation",
          coverage: "500,000 $",
          premium: "2000 $ annually",
          features: ["Cash Value Growth", "Tax Benefits", "Loan Options", "Guaranteed Death Benefit"]
        },
        {
          id: "POL-12345679",
          name: "Term Life Insurance",
          description: "Affordable temporary life insurance coverage",
          coverage: "750,000 $",
          premium: "800 $ annually",
          features: ["Lower Premiums", "Convertible Option", "Level Premiums", "Renewable"]
        },
        {
          id: "POL-12345680",
          name: "Universal Life Insurance",
          description: "Flexible premium permanent life insurance",
          coverage: "600,000 $",
          premium: "1500 $ annually",
          features: ["Flexible Premiums", "Investment Component", "Adjustable Death Benefit", "Tax Advantages"]
        },
        {
          id: "POL-12345681",
          name: "Disability Insurance",
          description: "Income protection in case of disability",
          coverage: "60% of income",
          premium: "1200 $ annually",
          features: ["Income Replacement", "Short & Long Term", "Partial Benefits", "Cost of Living Adjustments"]
        },
        {
          id: "POL-12345682",
          name: "Critical Illness Insurance",
          description: "Lump sum payment for critical illness diagnosis",
          coverage: "200,000 $",
          premium: "600 $ annually",
          features: ["Lump Sum Payment", "Multiple Conditions Covered", "No Restrictions on Use", "Return of Premium Option"]
        }
      ]
    },
    "CUS-123456|POL-87654321|Jane Doe": {
      DOB: "12.08.1988",
      Email: "<EMAIL>",
      Phone: "(*************",
      Address: "456 Oak Avenue, Los Angeles, CA 90210",
      Occupation: "Marketing Manager",
      "Annual Income": "$75,000",
      "Customer ID": "CUS-123456",
      "Policy Number": "POL-87654321",
      "Policy Type": "Term Life Insurance",
      Status: "Active",
      "Available Policies": [
        {
          id: "POL-87654321",
          name: "Term Life Insurance",
          description: "Affordable temporary life insurance coverage",
          coverage: "750,000 $",
          premium: "150 $ monthly",
          features: ["Affordable Premiums", "Convertible", "Level Death Benefit", "Renewable"]
        },
        {
          id: "POL-87654322",
          name: "Whole Life Insurance",
          description: "Permanent life insurance with guaranteed cash value",
          coverage: "500,000 $",
          premium: "300 $ monthly",
          features: ["Guaranteed Cash Value", "Dividends", "Loan Options", "Tax Benefits"]
        },
        {
          id: "POL-87654323",
          name: "Health Insurance",
          description: "Comprehensive medical coverage",
          coverage: "Unlimited",
          premium: "450 $ monthly",
          features: ["Preventive Care", "Prescription Coverage", "Specialist Access", "Emergency Care"]
        },
        {
          id: "POL-87654324",
          name: "Auto Insurance",
          description: "Complete vehicle protection coverage",
          coverage: "500,000 $ liability",
          premium: "120 $ monthly",
          features: ["Collision Coverage", "Comprehensive", "Liability Protection", "Roadside Assistance"]
        }
      ]
    },
    "CUS-111111|POL-11111111|Michael Johnson": {
      DOB: "22.11.1985",
      Email: "<EMAIL>",
      Phone: "(*************",
      Address: "789 Pine Street, Chicago, IL 60601",
      Occupation: "Financial Advisor",
      "Annual Income": "$95,000",
      "Customer ID": "CUS-111111",
      "Policy Number": "POL-11111111",
      "Policy Type": "Investment Life Insurance",
      Status: "Active",
      "Available Policies": [
        {
          id: "POL-11111111",
          name: "Investment Life Insurance",
          description: "Life insurance with investment opportunities",
          coverage: "800,000 $",
          premium: "1500 $ quarterly",
          features: ["Investment Growth", "Market Linked Returns", "Flexible Premiums", "Tax Efficiency"]
        },
        {
          id: "POL-11111112",
          name: "Retirement Annuity",
          description: "Guaranteed income for retirement",
          coverage: "Lifetime Income",
          premium: "2000 $ quarterly",
          features: ["Guaranteed Income", "Inflation Protection", "Death Benefits", "Tax Deferred Growth"]
        },
        {
          id: "POL-11111113",
          name: "Family Protection Plan",
          description: "Comprehensive family coverage",
          coverage: "1,000,000 $",
          premium: "800 $ quarterly",
          features: ["Family Coverage", "Child Benefits", "Spouse Protection", "Education Fund"]
        },
        {
          id: "POL-11111114",
          name: "Business Insurance",
          description: "Key person and business protection",
          coverage: "2,000,000 $",
          premium: "3000 $ quarterly",
          features: ["Key Person Coverage", "Business Loan Protection", "Buy-Sell Agreements", "Tax Benefits"]
        }
      ]
    }
  };

  const handleSearch = () => {
    setSearchClicked(true);
    const lookupKey = `${customerId.trim()}|${policyNumber.trim()}|${customerName.trim()}`;
    if (customerData[lookupKey]) {
      const customerDetails = customerData[lookupKey];
      setCurrentCustomerData({
        name: customerName,
        policyNumber: policyNumber,
        customerId: customerId,
        details: customerDetails
      });
      saveToLocalStorage(customerId, policyNumber, customerName); // Save on successful search
    } else {
      setCurrentCustomerData(null);
    }
  };

  const handleSelectPolicy = (policy: Policy, index: number) => {
    setSelectedPolicy(policy);
    setSelectedPolicyIndex(index);
  };

  const renderSearchSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 className="text-xl font-bold text-black mb-6 pb-3 border-b border-gray-200">
        Search Your Policy
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer ID</label>
          <input
            type="text"
            value={customerId}
            onChange={(e) => {
              const value = e.target.value;
              setCustomerId(value);

              // Try to auto-fill from customerData
              if (value) {
                const found = findCustomerById(value);
                if (found) {
                  setPolicyNumber(found.policy);
                  setCustomerName(found.name);
                }
              }
              // Optionally, keep the localStorage logic if you want
              if (value) tryAutoFill('id', value);
            }}
            placeholder="Enter customer ID"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Policy Number</label>
          <input
            type="text"
            value={policyNumber}
            onChange={(e) => {
              setPolicyNumber(e.target.value);
              if (e.target.value) tryAutoFill('policy', e.target.value);
            }}
            placeholder="Enter policy number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
          <input
            type="text"
            value={customerName}
            onChange={(e) => {
              setCustomerName(e.target.value);
              if (e.target.value) tryAutoFill('name', e.target.value);
            }}
            placeholder="Enter customer name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex items-end">
          <button
            onClick={handleSearch}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
          >
            <Search className="w-4 h-4" />
            Search
          </button>
        </div>
      </div>
    </div>
  );

  const renderAvailablePolicies = () => {
    if (!currentCustomerData) return null;
    const availablePolicies = currentCustomerData.details["Available Policies"] || [];
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-black">Yours Policies</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sum Assurance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Annual Premium
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {availablePolicies.map((policy: Policy, index: number) => {
                const status = index < 2 ? "Active" : "Inactive";
                const statusClass = status === "Active"
                  ? "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
                  : "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800";
                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {policy.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={statusClass}>{status}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 text-left">
                      {policy.coverage.includes("$") && !policy.coverage.includes("%")
                        ? `$${policy.coverage.replace(/\s*\$/g, "")}`
                        : policy.coverage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.premium}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleSelectPolicy(policy, index)}
                        className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                      >
                        Select
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderSelectedPolicyDetails = () => {
    if (!selectedPolicy || !currentCustomerData) return null;
    const paymentHistory = [
      { date: "01/15/2024", amount: "$200", status: "Paid" },
      { date: "12/15/2023", amount: "$200", status: "Paid" },
      { date: "11/15/2023", amount: "$200", status: "Paid" },
    ];
    const transactionHistory = [
      { id: "TNX 001", date: "03-01-2024", type: "Premium Payment", amount: "1200.00", remarks: "Increase Cash Value" },
      { id: "TNX 002", date: "04-27-2022", type: "Loan Repayment", amount: "2349.00", remarks: "Paid up additions" },
      { id: "TNX 003", date: "09-13-2021", type: "Dividend Credited", amount: "1750.00", remarks: "Cash Option" },
      { id: "TNX 004", date: "12-29-2019", type: "Dividend to Premium", amount: "3450.00", remarks: "Reduces Premium Cost" },
    ];
    return (
      <div className="space-y-8">
        <h2 className="text-xl font-semibold text-gray-900 pb-3 border-b border-gray-200">
          Selected Policy Details
        </h2>
        {/* Customer and Policy Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Customer Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Full Name</span>
                <span className="text-sm text-gray-900">{currentCustomerData.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Date of Birth</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.DOB}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Customer ID</span>
                <span className="text-sm text-gray-900">{currentCustomerData.customerId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Email</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.Email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Phone</span>
                <span className="text-sm text-gray-900">{currentCustomerData.details.Phone}</span>
              </div>
            </div>
          </div>
          {/* Coverage Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Coverage Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Coverage Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicy.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium Type</span>
                <span className="text-sm text-gray-900">Level Premium</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">Monthly</span>
              </div>
            </div>
          </div>
          {/* Policy Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Policy Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Number</span>
                <span className="text-sm text-gray-900">{selectedPolicy.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Issue Date</span>
                <span className="text-sm text-gray-900">01/01/2024</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.premium}</span>
              </div>
            </div>
          </div>
          {/* Financial Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Financial Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicy.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Cash Value</span>
                <span className="text-sm text-gray-900">$25,000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">Monthly</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Next Due Date</span>
                <span className="text-sm text-gray-900">01/01/2024</span>
              </div>
            </div>
          </div>
        </div>
        {/* Payment History and Riders */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Payment History</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {paymentHistory.map((payment, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* Active Riders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Active Riders</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Rider</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Coverage</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Critical Illness Rider</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$50,000</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Accidental Death Benefit</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$100,000</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                        Active
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-black">Transaction History</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Transaction ID</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Remarks</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {transactionHistory.map((transaction, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.type}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.remarks}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  const renderNoResultsFound = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">Customer not found. Please check your details.</p>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800 font-medium mb-2">Sample Test Data:</p>
          <div className="text-blue-700 text-sm space-y-1">
            <p>• CUS-567890 | POL-12345678 | John Smith</p>
            <p>• CUS-123456 | POL-87654321 | Jane Doe</p>
            <p>• CUS-111111 | POL-11111111 | Michael Johnson</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIllustrationSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <h3 className="text-lg font-bold text-black mb-4">Ready to proceed?</h3>
      <p className="text-gray-600 mb-6">Generate detailed policy illustrations and projections</p>
      <button
        className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
        onClick={() => {
          if (selectedPolicy && currentCustomerData) {
            // Save selected customer and policy data to context
            setSelectedCustomerData({
              name: currentCustomerData.name,
              policyNumber: currentCustomerData.policyNumber,
              customerId: currentCustomerData.customerId,
              details: currentCustomerData.details
            });

            setSelectedPolicyData({
              id: selectedPolicy.id,
              name: selectedPolicy.name,
              description: selectedPolicy.description,
              coverage: selectedPolicy.coverage,
              premium: selectedPolicy.premium,
              features: selectedPolicy.features
            });

            setActiveTab('as-is');
          }
        }}
        disabled={!selectedPolicy || !currentCustomerData}
      >
        <Calculator className="w-5 h-5" />
        📊 Go to Illustration
      </button>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Main Title */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Known Your Policy</h1>
        <p className="text-gray-600 dark:text-gray-400">Enter customer details to search and select a policy for illustration.</p>
      </div>
      {/* Search Section */}
      {renderSearchSection()}
      {/* Search Results */}
      {searchClicked && (
        <>
          {currentCustomerData ? (
            <>
              {renderAvailablePolicies()}
              {selectedPolicy && renderSelectedPolicyDetails()}
            </>
          ) : (
            renderNoResultsFound()
          )}
        </>
      )}
      {/* Info Message when no search performed */}
      {!searchClicked && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800">Enter customer details and click Search to view available policies.</p>
        </div>
      )}
      {/* Illustration Section */}
      {renderIllustrationSection()}
    </div>
  );
};

export default PolicySelection;