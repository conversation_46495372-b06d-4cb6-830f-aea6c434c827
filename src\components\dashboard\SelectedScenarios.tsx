import React, { useState } from 'react';
import { Edit2, Trash2, BarChart3, Calendar, Filter, Bookmark, Download, Mail, MessageCircle, FileText } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';

const SelectedScenarios: React.FC = () => {
  const { scenarios, selectedScenarios, toggleScenarioSelection, selectAllScenarios, deleteScenario, deleteMultipleScenarios, setActiveTab, updateScenario } = useDashboard();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [editingScenario, setEditingScenario] = useState<any>(null);
  const [editForm, setEditForm] = useState({
    name: '',
    asIsDetails: '',
    whatIfOptions: ['']
  });

  // Remove mock data for demonstration
  // const mockScenarios = [
  //   {
  //     id: '1',
  //     name: 'Whole Life Premium Analysis',
  //     policyId: 'POL-2024-001',
  //     asIsDetails: 'Current Policy Details',
  //     whatIfOptions: ['Increase Premium by 10%', 'Skip Premium Payments'],
  //     category: 'premium' as const,
  //     data: {},
  //     createdAt: new Date('2024-01-15'),
  //     updatedAt: new Date('2024-01-15'),
  //   },
  //   {
  //     id: '2',
  //     name: 'Face Amount Increase Scenario',
  //     policyId: 'POL-2024-001',
  //     asIsDetails: 'Base Scenario',
  //     whatIfOptions: ['Increase Face Amount by 25%'],
  //     category: 'face-amount' as const,
  //     data: {},
  //     createdAt: new Date('2024-01-14'),
  //     updatedAt: new Date('2024-01-14'),
  //   },
  //   {
  //     id: '3',
  //     name: 'Loan Repayment Strategy',
  //     policyId: 'POL-2024-001',
  //     asIsDetails: 'Current Policy Details',
  //     whatIfOptions: ['Full Loan Repayment', 'Partial Loan Repayment'],
  //     category: 'loan-repayment' as const,
  //     data: {},
  //     createdAt: new Date('2024-01-13'),
  //     updatedAt: new Date('2024-01-13'),
  //   },
  // ];

  // Use only real scenarios from context
  // const displayScenarios = scenarios.length > 0 ? scenarios : mockScenarios;

  const filteredScenarios = scenarios.filter(scenario => {
    const matchesSearch = scenario.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scenario.policyId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || scenario.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  const handleSelectAll = (checked: boolean) => {
    selectAllScenarios(checked);
  };

  const handleDeleteMultiple = () => {
    if (selectedScenarios.length > 0) {
      deleteMultipleScenarios(selectedScenarios);
    }
  };

  const handleAnalyze = () => {
    if (selectedScenarios.length === 0) {
      alert('Please select at least one scenario to analyze');
      return;
    }
    setActiveTab('analysis-reports');
  };

  const handleExportData = () => {
    if (selectedScenarios.length === 0) {
      alert('Please select at least one scenario to export');
      return;
    }
    setShowExportOptions(true);
  };

  const handleDownload = () => {
    const selectedScenariosData = scenarios.filter(scenario => 
      selectedScenarios.includes(scenario.id)
    );
    
    const csvData = selectedScenariosData.map(scenario => [
      scenario.name,
      scenario.policyId,
      scenario.category,
      scenario.asIsDetails,
      scenario.whatIfOptions.join('; '),
      scenario.createdAt.toLocaleDateString(),
      scenario.updatedAt.toLocaleDateString()
    ].join(',')).join('\n');
    
    const headers = 'Scenario Name,Policy ID,Category,AS-IS Details,What-If Options,Created Date,Updated Date\n';
    const csv = headers + csvData;
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'selected-scenarios.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    setShowExportOptions(false);
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent('Selected Insurance Scenarios');
    const body = encodeURIComponent(`Selected scenarios for analysis:\n\n${selectedScenarios.length} scenarios selected for review.`);
    window.open(`mailto:?subject=${subject}&body=${body}`);
    setShowExportOptions(false);
  };

  const handleEditScenario = (scenario: any) => {
    setEditingScenario(scenario);
    setEditForm({
      name: scenario.name,
      asIsDetails: scenario.asIsDetails,
      whatIfOptions: scenario.whatIfOptions.length > 0 ? scenario.whatIfOptions : ['']
    });
  };

  const handleSaveEdit = () => {
    if (!editingScenario) return;

    const updates = {
      name: editForm.name,
      asIsDetails: editForm.asIsDetails,
      whatIfOptions: editForm.whatIfOptions.filter(option => option.trim() !== ''),
      updatedAt: new Date()
    };

    updateScenario(editingScenario.id, updates);
    setEditingScenario(null);
    setEditForm({ name: '', asIsDetails: '', whatIfOptions: [''] });
  };

  const handleCancelEdit = () => {
    setEditingScenario(null);
    setEditForm({ name: '', asIsDetails: '', whatIfOptions: [''] });
  };

  const handleEditFormChange = (field: string, value: string) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleWhatIfOptionChange = (index: number, value: string) => {
    setEditForm(prev => ({
      ...prev,
      whatIfOptions: prev.whatIfOptions.map((option, i) => i === index ? value : option)
    }));
  };

  const addWhatIfOption = () => {
    setEditForm(prev => ({
      ...prev,
      whatIfOptions: [...prev.whatIfOptions, '']
    }));
  };

  const removeWhatIfOption = (index: number) => {
    setEditForm(prev => ({
      ...prev,
      whatIfOptions: prev.whatIfOptions.filter((_, i) => i !== index)
    }));
  };

  const handleWhatsAppShare = () => {
    const message = encodeURIComponent(`Selected Insurance Scenarios - ${selectedScenarios.length} scenarios ready for analysis.`);
    window.open(`https://wa.me/?text=${message}`);
    setShowExportOptions(false);
  };

  const categoryLabels = {
    'as-is': 'As-Is Configuration',
    'face-amount': 'Face Amount',
    'premium': 'Premium',
    'income': 'Income',
    'loan-repayment': 'Loan Repayment',
    'interest-rate': 'Interest Rate',
    'policy-lapse': 'Policy Lapse',
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <Bookmark className="w-6 h-6 text-blue-600" />
            <span>Selected Scenarios</span>
          </h1>
          <p className="text-gray-600">Manage your saved illustration scenarios for analysis.</p>
        </div>
        
        {/* Selected Scenarios Counter */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
          <div className="flex items-center space-x-2">
            <Bookmark className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">
              {selectedScenarios.length} scenario(s) selected
            </span>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <Card>
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search scenarios..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="flex items-center space-x-3">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="as-is">As-Is Configuration</option>
              <option value="face-amount">Face Amount</option>
              <option value="premium">Premium</option>
              <option value="income">Income</option>
              <option value="loan-repayment">Loan Repayment</option>
              <option value="interest-rate">Interest Rate</option>
              <option value="policy-lapse">Policy Lapse</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Bulk Actions */}
      {selectedScenarios.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-blue-900">
                {selectedScenarios.length} scenario(s) selected for analysis
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleExportData}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export Data</span>
              </Button>
              <Button
                onClick={handleAnalyze}
                variant="secondary"
                size="sm"
                className="flex items-center space-x-2"
              >
                <BarChart3 className="w-4 h-4" />
                <span>Analyze Selected</span>
              </Button>
              <Button
                onClick={handleDeleteMultiple}
                variant="danger"
                size="sm"
                className="flex items-center space-x-2"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete Selected</span>
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Export Options Modal */}
      {showExportOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Export Selected Scenarios</h3>
            <div className="space-y-3">
              <button
                onClick={handleDownload}
                className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <Download className="w-5 h-5 text-blue-600" />
                <span className="font-medium text-gray-900 dark:text-gray-100">Download CSV</span>
              </button>
              <button
                onClick={handleEmailShare}
                className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <Mail className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-900 dark:text-gray-100">Send via Email</span>
              </button>
              <button
                onClick={handleWhatsAppShare}
                className="w-full flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <MessageCircle className="w-5 h-5 text-green-600" />
                <span className="font-medium text-gray-900 dark:text-gray-100">Share on WhatsApp</span>
              </button>
            </div>
            <div className="mt-4 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowExportOptions(false)}
              >
                Cancel
              </Button>
            </div>
          </Card>
        </div>
      )}

      {/* Scenarios List */}
      <Card>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Available Scenarios</h3>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedScenarios.length === filteredScenarios.length && filteredScenarios.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>
          </div>

          <div className="space-y-3">
            {filteredScenarios.map((scenario) => (
              <div
                key={scenario.id}
                className={`flex items-center space-x-4 p-4 border rounded-lg transition-all ${
                  selectedScenarios.includes(scenario.id)
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:bg-gray-50'
                }`}
              >
                <input
                  type="checkbox"
                  checked={selectedScenarios.includes(scenario.id)}
                  onChange={() => toggleScenarioSelection(scenario.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                
                <div className="flex-1 space-y-1">
                  <div className="flex items-center space-x-3">
                    <h4 className="font-medium text-gray-900">{scenario.name}</h4>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                      {categoryLabels[scenario.category]}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">Policy: {scenario.policyId}</p>
                  <p className="text-sm text-gray-500">
                    What-If: {scenario.whatIfOptions.join(', ')}
                  </p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>Created: {scenario.createdAt.toLocaleDateString()}</span>
                    </span>
                    <span>Updated: {scenario.updatedAt.toLocaleDateString()}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleEditScenario(scenario)}
                    className="flex items-center space-x-1"
                  >
                    <Edit2 className="w-4 h-4" />
                    <span>Edit</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => deleteScenario(scenario.id)}
                    className="flex items-center space-x-1 text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {filteredScenarios.length === 0 && (
            <div className="text-center py-8">
              <Bookmark className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No scenarios found matching your criteria.</p>
              <p className="text-sm text-gray-400 mt-1">Create scenarios in the Illustration Manager to get started.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Get Illustration Button - Bottom Right */}
      <div className="fixed bottom-6 right-6">
        <div className="flex flex-col space-y-3">
          <Button
            onClick={handleAnalyze}
            size="lg"
            className={`flex items-center space-x-2 shadow-lg hover:shadow-xl transition-all ${
              selectedScenarios.length === 0 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:scale-105'
            }`}
            disabled={selectedScenarios.length === 0}
          >
            <BarChart3 className="w-5 h-5" />
            <span>Get Illustration</span>
          </Button>
          <Button
            onClick={() => setActiveTab('illustration-manager')}
            size="lg"
            variant="outline"
            className="flex items-center space-x-2 shadow-lg hover:shadow-xl transition-all hover:scale-105"
          >
            <FileText className="w-5 h-5" />
            <span>Create New</span>
          </Button>
        </div>
      </div>

      {/* Edit Scenario Modal */}
      {editingScenario && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Edit Scenario: {editingScenario.name}
            </h3>

            <div className="space-y-4">
              {/* Scenario Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Scenario Name
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => handleEditFormChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Enter scenario name"
                />
              </div>

              {/* AS-IS Details */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  AS-IS Details
                </label>
                <textarea
                  value={editForm.asIsDetails}
                  onChange={(e) => handleEditFormChange('asIsDetails', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Enter AS-IS details"
                />
              </div>

              {/* What-If Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  What-If Options
                </label>
                {editForm.whatIfOptions.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => handleWhatIfOptionChange(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                      placeholder={`What-if option ${index + 1}`}
                    />
                    {editForm.whatIfOptions.length > 1 && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeWhatIfOption(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={addWhatIfOption}
                  className="mt-2"
                >
                  Add What-If Option
                </Button>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={handleCancelEdit}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveEdit}
                disabled={!editForm.name.trim()}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedScenarios;