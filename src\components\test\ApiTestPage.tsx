import React, { useState, useEffect } from 'react';
import { ApiService } from '../../services/api';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';

const ApiTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { scenarios, selectedScenarios, addScenario } = useDashboard();

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testApiConnection = async () => {
    try {
      const health = await ApiService.healthCheck();
      addTestResult(`✅ Health check passed: ${health.status}`);
      return true;
    } catch (error) {
      addTestResult(`❌ Health check failed: ${error}`);
      return false;
    }
  };

  const testScenarioCreation = async () => {
    try {
      const testScenario = {
        name: `Test Scenario ${Date.now()}`,
        policyId: 'TEST-001',
        asIsDetails: 'Test AS-IS details',
        whatIfOptions: ['Test option 1', 'Test option 2'],
        category: 'premium' as const,
        data: { test: true }
      };

      await addScenario(testScenario);
      addTestResult(`✅ Scenario created successfully`);
      return true;
    } catch (error) {
      addTestResult(`❌ Scenario creation failed: ${error}`);
      return false;
    }
  };

  const testScenarioRetrieval = async () => {
    try {
      const scenarios = await ApiService.getScenarios();
      addTestResult(`✅ Retrieved ${scenarios.length} scenarios`);
      return scenarios.length > 0;
    } catch (error) {
      addTestResult(`❌ Scenario retrieval failed: ${error}`);
      return false;
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    addTestResult('🧪 Starting API integration tests...');
    
    // Test 1: API Connection
    const healthOk = await testApiConnection();
    if (!healthOk) {
      setIsLoading(false);
      return;
    }

    // Test 2: Scenario Creation
    const createOk = await testScenarioCreation();
    if (!createOk) {
      setIsLoading(false);
      return;
    }

    // Test 3: Scenario Retrieval
    const retrieveOk = await testScenarioRetrieval();
    if (!retrieveOk) {
      setIsLoading(false);
      return;
    }

    addTestResult('🎉 All tests passed! Frontend-Backend integration is working.');
    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">API Integration Test</h1>
        <p className="text-gray-600">Test the connection between frontend and backend.</p>
      </div>

      <Card>
        <div className="space-y-4">
          <div className="flex space-x-4">
            <Button 
              onClick={runAllTests} 
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              {isLoading ? '🔄 Testing...' : '🧪 Run Tests'}
            </Button>
            <Button 
              onClick={clearResults} 
              variant="outline"
              disabled={isLoading}
            >
              Clear Results
            </Button>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Current State:</h3>
            <div className="text-sm space-y-1">
              <div>📊 Scenarios in context: {scenarios.length}</div>
              <div>✅ Selected scenarios: {selectedScenarios.length}</div>
              <div>🔗 Backend URL: http://localhost:8000</div>
            </div>
          </div>

          {testResults.length > 0 && (
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              <h3 className="text-white font-semibold mb-2">Test Results:</h3>
              {testResults.map((result, index) => (
                <div key={index} className="mb-1">{result}</div>
              ))}
            </div>
          )}
        </div>
      </Card>

      <Card>
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Current Scenarios</h3>
          {scenarios.length === 0 ? (
            <p className="text-gray-500">No scenarios found. Create some scenarios to test the integration.</p>
          ) : (
            <div className="space-y-2">
              {scenarios.map((scenario) => (
                <div key={scenario.id} className="p-3 border rounded-lg">
                  <div className="font-medium">{scenario.name}</div>
                  <div className="text-sm text-gray-600">
                    ID: {scenario.id} | Category: {scenario.category}
                  </div>
                  <div className="text-xs text-gray-500">
                    Created: {scenario.createdAt.toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>

      <Card>
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Selected Scenarios</h3>
          {selectedScenarios.length === 0 ? (
            <p className="text-gray-500">No scenarios selected.</p>
          ) : (
            <div className="space-y-2">
              {selectedScenarios.map((id) => {
                const scenario = scenarios.find(s => s.id === id);
                return (
                  <div key={id} className="p-3 border rounded-lg bg-blue-50">
                    <div className="font-medium">
                      {scenario ? scenario.name : `Unknown scenario (${id})`}
                    </div>
                    <div className="text-sm text-gray-600">ID: {id}</div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default ApiTestPage;
