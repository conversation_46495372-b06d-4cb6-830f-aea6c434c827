[{"name": "AS-IS Configuration - <PERSON> (POL-123)", "policyId": "POL-123", "asIsDetails": "Retirement Age: 65, Maturity Age: 121", "whatIfOptions": ["Face Amount: $500,000", "Annual Premium: $5,000"], "category": "as-is", "data": {"source": "AsIsPage"}, "id": "32f68902-6384-44e1-8816-14ae19aa41c9", "userId": "demo-user-id", "createdAt": "2025-07-10T11:18:40.457788+00:00", "updatedAt": "2025-07-10T11:18:40.457802+00:00"}, {"name": "Income: Retirem<PERSON> Withdrawal", "policyId": "POL-123", "asIsDetails": "Current Policy Details", "whatIfOptions": ["Income: Retirement Withdrawal ($50,000 annual)"], "category": "income", "data": {"source": "IncomePage"}, "id": "11c8f488-88a0-439e-a2f4-93e8d8244809", "userId": "demo-user-id", "createdAt": "2025-07-10T11:18:44.535817+00:00", "updatedAt": "2025-07-10T11:18:44.535833+00:00"}, {"name": "Face Amount Increase <PERSON>", "policyId": "POL-123", "asIsDetails": "Current Death Benefit: $500,000", "whatIfOptions": ["Increase to $750,000"], "category": "face-amount", "data": {"source": "FaceAmountPage"}, "id": "fbb4b9a4-9f6d-4a8f-b259-c37d57dfe529", "userId": "demo-user-id", "createdAt": "2025-07-10T11:18:48.635173+00:00", "updatedAt": "2025-07-10T11:18:48.635195+00:00"}]