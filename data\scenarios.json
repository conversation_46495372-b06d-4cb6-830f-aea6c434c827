[{"name": "Updated Test Scenario", "policyId": "POL-001", "asIsDetails": "Retirement Age: 65, Maturity Age: 121", "whatIfOptions": ["Face Amount: 500,000", "Annual Premium: 5,000"], "category": "as-is", "data": {"source": "AsIsPage"}, "id": "ccaa0992-3845-4f29-877f-7ac85275bccf", "userId": "demo-user-id", "createdAt": "2025-07-10T08:39:35.752071+00:00", "updatedAt": "2025-07-10T08:40:05.320797+00:00"}, {"name": "Face Amount <PERSON><PERSON> - Test Customer", "policyId": "POL-001", "asIsDetails": "Current Death Benefit: $500,000", "whatIfOptions": ["Increase to $750,000"], "category": "face-amount", "data": {"source": "FaceAmountPage"}, "id": "2062d1b8-2b18-494f-9250-5f710a9014cf", "userId": "demo-user-id", "createdAt": "2025-07-10T08:39:39.840269+00:00", "updatedAt": "2025-07-10T08:39:39.840289+00:00"}, {"name": "Income: Retirem<PERSON> Withdrawal", "policyId": "POL-001", "asIsDetails": "Current Policy Details", "whatIfOptions": ["Income: Retirement Withdrawal (withdrawal - $50,000 annual)"], "category": "income", "data": {"source": "IncomePage"}, "id": "6bbdd20f-18b8-4719-b88f-4fdeac06aac7", "userId": "demo-user-id", "createdAt": "2025-07-10T08:39:43.894907+00:00", "updatedAt": "2025-07-10T08:39:43.894918+00:00"}]