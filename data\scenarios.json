[{"name": "Test Income Scenario", "policyId": "POL-001", "asIsDetails": "Current Policy Details", "whatIfOptions": ["Income: Test ($50,000 annual)"], "category": "income", "data": {"test": true}, "id": "5152a491-8f22-4122-a3a1-90aab419d755", "userId": "demo-user-id", "createdAt": "2025-07-10T09:06:12.417789+00:00", "updatedAt": "2025-07-10T09:06:12.417810+00:00"}, {"name": "Income: Retirem<PERSON> Withdrawal", "policyId": "POL-001", "asIsDetails": "Current Policy Details", "whatIfOptions": ["Income: Retirement Withdrawal ($50,000 annual)"], "category": "income", "data": {"type": "withdrawal"}, "id": "2cca80af-496a-4cd4-83c9-c14caf464f6d", "userId": "demo-user-id", "createdAt": "2025-07-10T09:06:20.566674+00:00", "updatedAt": "2025-07-10T09:06:20.566685+00:00"}, {"name": "Income: Policy Loan", "policyId": "POL-001", "asIsDetails": "Current Policy Details", "whatIfOptions": ["Income: Policy Loan ($25,000 annual)"], "category": "income", "data": {"type": "loan"}, "id": "4118df71-77f7-4d35-a5e1-17d3412f6ae0", "userId": "demo-user-id", "createdAt": "2025-07-10T09:06:28.747227+00:00", "updatedAt": "2025-07-10T09:06:28.747252+00:00"}]