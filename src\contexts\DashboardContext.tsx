import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Policy, Scenario, DashboardState, SelectedCustomerData, SelectedPolicyData } from '../types';
import { ApiService, ApiUtils } from '../services/api';

interface DashboardContextType extends DashboardState {
  setActiveTab: (tab: string) => void;
  setCurrentPolicy: (policy: Policy | null) => void;
  setSelectedCustomerData: (customerData: SelectedCustomerData | null) => void;
  setSelectedPolicyData: (policyData: SelectedPolicyData | null) => void;
  addScenario: (scenario: Scenario) => Promise<void>;
  updateScenario: (id: string, updates: Partial<Scenario>) => Promise<void>;
  deleteScenario: (id: string) => Promise<void>;
  deleteMultipleScenarios: (ids: string[]) => Promise<void>;
  toggleScenarioSelection: (id: string) => void;
  selectAllScenarios: (select: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

const initialState: DashboardState = {
  activeTab: 'dashboard',
  currentPolicy: null,
  scenarios: [],
  selectedScenarios: [],
  selectedCustomerData: null,
  selectedPolicyData: null,
  loading: false,
  error: null,
};

type DashboardAction =
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_POLICY'; payload: Policy | null }
  | { type: 'SET_SELECTED_CUSTOMER_DATA'; payload: SelectedCustomerData | null }
  | { type: 'SET_SELECTED_POLICY_DATA'; payload: SelectedPolicyData | null }
  | { type: 'ADD_SCENARIO'; payload: Scenario }
  | { type: 'SET_SCENARIOS'; payload: Scenario[] }
  | { type: 'UPDATE_SCENARIO'; payload: { id: string; updates: Partial<Scenario> } }
  | { type: 'DELETE_SCENARIO'; payload: string }
  | { type: 'DELETE_MULTIPLE_SCENARIOS'; payload: string[] }
  | { type: 'TOGGLE_SCENARIO_SELECTION'; payload: string }
  | { type: 'SELECT_ALL_SCENARIOS'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload };
    case 'SET_CURRENT_POLICY':
      return { ...state, currentPolicy: action.payload };
    case 'SET_SELECTED_CUSTOMER_DATA':
      return { ...state, selectedCustomerData: action.payload };
    case 'SET_SELECTED_POLICY_DATA':
      return { ...state, selectedPolicyData: action.payload };
    case 'ADD_SCENARIO':
      return { ...state, scenarios: [...state.scenarios, action.payload] };
    case 'SET_SCENARIOS':
      return { ...state, scenarios: action.payload };
    case 'UPDATE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.map(scenario =>
          scenario.id === action.payload.id
            ? { ...scenario, ...action.payload.updates }
            : scenario
        ),
      };
    case 'DELETE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => scenario.id !== action.payload),
        selectedScenarios: state.selectedScenarios.filter(id => id !== action.payload),
      };
    case 'DELETE_MULTIPLE_SCENARIOS':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => !action.payload.includes(scenario.id)),
        selectedScenarios: [],
      };
    case 'TOGGLE_SCENARIO_SELECTION':
      return {
        ...state,
        selectedScenarios: state.selectedScenarios.includes(action.payload)
          ? state.selectedScenarios.filter(id => id !== action.payload)
          : [...state.selectedScenarios, action.payload],
      };
    case 'SELECT_ALL_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload ? state.scenarios.map(s => s.id) : [],
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
};

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  // Load data from backend on mount
  useEffect(() => {
    const loadInitialData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      try {
        // Load scenarios and selected scenarios in parallel
        const [scenarios, selectedScenarios] = await Promise.all([
          ApiService.getScenarios(),
          ApiService.getSelectedScenarios()
        ]);

        // Set scenarios
        dispatch({ type: 'SET_SCENARIOS', payload: scenarios });

        // Set selected scenarios
        selectedScenarios.forEach((id) => {
          dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id });
        });

      } catch (error) {
        const errorMessage = error instanceof Error
          ? ApiUtils.formatErrorMessage(error)
          : 'Failed to load data from server';

        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        console.error('Failed to load initial data:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadInitialData();
  }, []);

  // Auto-save selected scenarios when they change (but not on initial load)
  const [isInitialLoad, setIsInitialLoad] = React.useState(true);

  useEffect(() => {
    if (isInitialLoad) {
      setIsInitialLoad(false);
      return;
    }

    const saveSelectedScenarios = async () => {
      try {
        await ApiService.updateSelectedScenarios(state.selectedScenarios);
      } catch (error) {
        console.error('Failed to save selected scenarios:', error);
        // Don't show error to user for auto-save failures
      }
    };

    // Debounce the save operation
    const timeoutId = setTimeout(saveSelectedScenarios, 500);
    return () => clearTimeout(timeoutId);
  }, [state.selectedScenarios, isInitialLoad]);

  return (
    <DashboardContext.Provider value={{
      ...state,
      setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
      setCurrentPolicy: (policy: Policy | null) => dispatch({ type: 'SET_CURRENT_POLICY', payload: policy }),
      setSelectedCustomerData: (customerData: SelectedCustomerData | null) => dispatch({ type: 'SET_SELECTED_CUSTOMER_DATA', payload: customerData }),
      setSelectedPolicyData: (policyData: SelectedPolicyData | null) => dispatch({ type: 'SET_SELECTED_POLICY_DATA', payload: policyData }),
      addScenario: async (scenario: Scenario) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          await ApiService.createScenario(scenario);

          // Reload scenarios from backend to get the correct ID and ensure consistency
          const scenarios = await ApiService.getScenarios();
          dispatch({ type: 'SET_SCENARIOS', payload: scenarios });

          // Automatically select the newly created scenario
          if (scenarios.length > 0) {
            const newScenario = scenarios[scenarios.length - 1]; // Get the latest scenario
            dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: newScenario.id });
          }

        } catch (error) {
          const errorMessage = error instanceof Error
            ? ApiUtils.formatErrorMessage(error)
            : 'Failed to create scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to create scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      updateScenario: async (id: string, updates: Partial<Scenario>) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          await ApiService.updateScenario(id, updates);
          dispatch({ type: 'UPDATE_SCENARIO', payload: { id, updates } });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? ApiUtils.formatErrorMessage(error)
            : 'Failed to update scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to update scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteScenario: async (id: string) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          await ApiService.deleteScenario(id);
          dispatch({ type: 'DELETE_SCENARIO', payload: id });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? ApiUtils.formatErrorMessage(error)
            : 'Failed to delete scenario';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete scenario:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      deleteMultipleScenarios: async (ids: string[]) => {
        try {
          dispatch({ type: 'SET_LOADING', payload: true });
          await ApiService.deleteMultipleScenarios(ids);
          dispatch({ type: 'DELETE_MULTIPLE_SCENARIOS', payload: ids });
        } catch (error) {
          const errorMessage = error instanceof Error
            ? ApiUtils.formatErrorMessage(error)
            : 'Failed to delete scenarios';
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          console.error('Failed to delete multiple scenarios:', error);
        } finally {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      },
      toggleScenarioSelection: (id: string) => dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id }),
      selectAllScenarios: (select: boolean) => dispatch({ type: 'SELECT_ALL_SCENARIOS', payload: select }),
      setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
      setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};